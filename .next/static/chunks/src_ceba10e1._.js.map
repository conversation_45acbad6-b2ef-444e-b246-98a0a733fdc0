{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mUACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/lib/services/openrouter-ai.ts"], "sourcesContent": ["const API_KEY = process.env.OPENROUTER_API_KEY!\n\nexport interface ReviewAnalysisResult {\n  pros: Array<{ text: string; count: number }>\n  cons: Array<{ text: string; count: number }>\n  sentiment_score?: number\n  summary?: string\n}\n\nexport interface MarketAnalysisResult {\n  market_size: string\n  competition_level: 'low' | 'medium' | 'high'\n  opportunity_score: number\n  key_insights: string[]\n  recommended_actions: string[]\n  price_analysis: {\n    average_price: number\n    price_range: { min: number; max: number }\n    price_trend: 'increasing' | 'decreasing' | 'stable'\n  }\n}\n\nexport class OpenRouterAIService {\n  static async analyzeReviews(\n    reviews: string[],\n    locale: 'en' | 'fr' = 'en'\n  ): Promise<ReviewAnalysisResult> {\n    try {\n      const response = await fetch('/api/ai/analyze-reviews', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ reviews, locale })\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to analyze reviews')\n      }\n\n      return await response.json()\n    } catch (error) {\n      console.error('Error analyzing reviews:', error)\n      throw new Error('Failed to analyze reviews')\n    }\n  }\n\n  static async generateMarketAnalysis(\n    products: any[],\n    niche: string\n  ): Promise<MarketAnalysisResult> {\n    try {\n      const response = await fetch('/api/ai/market-analysis', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ products, niche })\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to generate market analysis')\n      }\n\n      return await response.json()\n    } catch (error) {\n      console.error('Error generating market analysis:', error)\n      throw new Error('Failed to generate market analysis')\n    }\n  }\n\n  static async generateProductInsights(\n    productDetails: any,\n    reviews: string[]\n  ): Promise<{\n    strengths: string[]\n    weaknesses: string[]\n    opportunities: string[]\n    threats: string[]\n    recommendations: string[]\n  }> {\n    try {\n      const requestBody = {\n        model: \"deepseek/deepseek-r1:free\",\n        messages: [{\n          role: \"user\",\n          content: `Analyze this product and its reviews to provide strategic insights:\n\n                   Product: ${productDetails.product_title}\n                   Price: ${productDetails.product_price}\n                   Rating: ${productDetails.product_star_rating}\n                   Reviews Count: ${productDetails.product_num_ratings}\n\n                   Sample Reviews: ${reviews.slice(0, 50).join('\\n')}\n\n                   Provide a SWOT analysis and recommendations in JSON format:\n                   {\n                     \"strengths\": [\"strength1\", \"strength2\", \"strength3\"],\n                     \"weaknesses\": [\"weakness1\", \"weakness2\", \"weakness3\"],\n                     \"opportunities\": [\"opportunity1\", \"opportunity2\", \"opportunity3\"],\n                     \"threats\": [\"threat1\", \"threat2\", \"threat3\"],\n                     \"recommendations\": [\"rec1\", \"rec2\", \"rec3\"]\n                   }\n\n                   IMPORTANT: Respond ONLY with valid JSON, no markdown or backticks.`\n        }]\n      }\n\n      const response = await fetch(\"https://openrouter.ai/api/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Authorization\": `Bearer ${API_KEY}`,\n          \"HTTP-Referer\": \"https://github.com/amazon-product-insights\",\n          \"X-Title\": \"Amazon Product Insights\",\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(requestBody)\n      })\n\n      if (!response.ok) {\n        throw new Error(`API request failed: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n      const content = data.choices[0]?.message?.content\n\n      if (!content) {\n        throw new Error('No content received from AI service')\n      }\n\n      // Clean the response and parse JSON\n      const cleanedContent = content.replace(/```json|```/g, '').trim()\n      return JSON.parse(cleanedContent)\n    } catch (error) {\n      console.error('Error generating product insights:', error)\n      throw new Error('Failed to generate product insights')\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAgB;AAAhB,MAAM,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kBAAkB;AAsBvC,MAAM;IACX,aAAa,eACX,OAAiB,EACjB,SAAsB,IAAI,EACK;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAS;gBAAO;YACzC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,uBACX,QAAe,EACf,KAAa,EACkB;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAU;gBAAM;YACzC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,wBACX,cAAmB,EACnB,OAAiB,EAOhB;QACD,IAAI;YACF,MAAM,cAAc;gBAClB,OAAO;gBACP,UAAU;oBAAC;wBACT,MAAM;wBACN,SAAS,CAAC;;4BAEQ,EAAE,eAAe,aAAa,CAAC;0BACjC,EAAE,eAAe,aAAa,CAAC;2BAC9B,EAAE,eAAe,mBAAmB,CAAC;kCAC9B,EAAE,eAAe,mBAAmB,CAAC;;mCAEpC,EAAE,QAAQ,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM;;;;;;;;;;;qFAWgB,CAAC;oBAC9E;iBAAE;YACJ;YAEA,MAAM,WAAW,MAAM,MAAM,iDAAiD;gBAC5E,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,SAAS;oBACpC,gBAAgB;oBAChB,WAAW;oBACX,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,UAAU,EAAE;YAC9D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,UAAU,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS;YAE1C,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,MAAM,iBAAiB,QAAQ,OAAO,CAAC,gBAAgB,IAAI,IAAI;YAC/D,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/lib/services/amazon-api.ts"], "sourcesContent": ["import axios from 'axios'\n\nconst RAPIDAPI_KEY = process.env.RAPIDAPI_KEY!\nconst BASE_URL = 'https://real-time-amazon-data.p.rapidapi.com'\n\nconst apiClient = axios.create({\n  baseURL: BASE_URL,\n  headers: {\n    'x-rapidapi-key': RAPIDAPI_KEY,\n    'x-rapidapi-host': 'real-time-amazon-data.p.rapidapi.com'\n  }\n})\n\nexport interface AmazonProduct {\n  asin: string\n  product_title: string\n  product_price: string\n  product_original_price?: string\n  currency: string\n  product_star_rating: string\n  product_num_ratings: number\n  product_url: string\n  product_photo: string\n  product_availability: string\n  is_best_seller: boolean\n  is_amazon_choice: boolean\n  is_prime: boolean\n  climate_pledge_friendly: boolean\n  sales_volume?: string\n  delivery?: string\n}\n\nexport interface AmazonReview {\n  review_id: string\n  review_title: string\n  review_comment: string\n  review_star_rating: number\n  review_date: string\n  review_author: string\n  review_author_avatar?: string\n  is_verified_purchase: boolean\n  helpful_vote_statement?: string\n  review_country: string\n  review_images?: string[]\n  review_videos?: string[]\n}\n\nexport interface ProductDetails {\n  asin: string\n  product_title: string\n  product_price: string\n  product_original_price?: string\n  currency: string\n  product_star_rating: string\n  product_num_ratings: number\n  product_url: string\n  product_photos: string[]\n  product_details: Record<string, any>\n  product_description: string\n  product_information: Record<string, any>\n  product_specifications: Record<string, any>\n  customers_say: {\n    keywords: Array<{\n      keyword: string\n      sentiment_score: number\n      mentions_count: number\n    }>\n  }\n  category_tree: Array<{\n    name: string\n    link: string\n  }>\n}\n\nexport class AmazonAPIService {\n  static async searchProducts(query: string, page = 1, country = 'US'): Promise<{\n    data: AmazonProduct[]\n    total_products: number\n    country: string\n    domain: string\n  }> {\n    try {\n      const response = await fetch(`/api/amazon/search?query=${encodeURIComponent(query)}&page=${page}&country=${country}`)\n      if (!response.ok) {\n        throw new Error('Failed to search products')\n      }\n      return await response.json()\n    } catch (error) {\n      console.error('Error searching products:', error)\n      throw new Error('Failed to search products')\n    }\n  }\n\n  static async getProductDetails(asin: string, country = 'US'): Promise<ProductDetails> {\n    try {\n      const response = await fetch(`/api/amazon/product/${asin}?country=${country}`)\n      if (!response.ok) {\n        throw new Error('Failed to fetch product details')\n      }\n      return await response.json()\n    } catch (error) {\n      console.error('Error fetching product details:', error)\n      throw new Error('Failed to fetch product details')\n    }\n  }\n\n  static async getProductReviews(\n    asin: string,\n    page = 1,\n    country = 'US',\n    sortBy = 'TOP_REVIEWS'\n  ): Promise<{\n    data: AmazonReview[]\n    total_reviews: number\n    country: string\n    domain: string\n  }> {\n    try {\n      const response = await fetch(`/api/amazon/reviews/${asin}?page=${page}&country=${country}&sortBy=${sortBy}`)\n      if (!response.ok) {\n        throw new Error('Failed to fetch product reviews')\n      }\n      return await response.json()\n    } catch (error) {\n      console.error('Error fetching product reviews:', error)\n      throw new Error('Failed to fetch product reviews')\n    }\n  }\n\n  static async getProductsByCategory(\n    categoryId: string,\n    page = 1,\n    country = 'US'\n  ): Promise<{\n    data: AmazonProduct[]\n    total_products: number\n    country: string\n    domain: string\n  }> {\n    try {\n      const response = await apiClient.get('/products-by-category', {\n        params: {\n          category_id: categoryId,\n          page: page.toString(),\n          country\n        }\n      })\n      return response.data\n    } catch (error) {\n      console.error('Error fetching products by category:', error)\n      throw new Error('Failed to fetch products by category')\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAEqB;AAFrB;;AAEA,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY;AAC7C,MAAM,WAAW;AAEjB,MAAM,YAAY,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,kBAAkB;QAClB,mBAAmB;IACrB;AACF;AA+DO,MAAM;IACX,aAAa,eAAe,KAAa,EAAE,OAAO,CAAC,EAAE,UAAU,IAAI,EAKhE;QACD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,yBAAyB,EAAE,mBAAmB,OAAO,MAAM,EAAE,KAAK,SAAS,EAAE,SAAS;YACpH,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,kBAAkB,IAAY,EAAE,UAAU,IAAI,EAA2B;QACpF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,KAAK,SAAS,EAAE,SAAS;YAC7E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,kBACX,IAAY,EACZ,OAAO,CAAC,EACR,UAAU,IAAI,EACd,SAAS,aAAa,EAMrB;QACD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,KAAK,MAAM,EAAE,KAAK,SAAS,EAAE,QAAQ,QAAQ,EAAE,QAAQ;YAC3G,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,sBACX,UAAkB,EAClB,OAAO,CAAC,EACR,UAAU,IAAI,EAMb;QACD,IAAI;YACF,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,yBAAyB;gBAC5D,QAAQ;oBACN,aAAa;oBACb,MAAM,KAAK,QAAQ;oBACnB;gBACF;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/app/dashboard/market-analysis/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { \n  TrendingUp, \n  TrendingDown,\n  BarChart3,\n  DollarSign,\n  Users,\n  Package,\n  AlertCircle,\n  CheckCircle,\n  Loader2\n} from 'lucide-react'\nimport { OpenRouterAIService, type MarketAnalysisResult } from '@/lib/services/openrouter-ai'\nimport { AmazonAPIService } from '@/lib/services/amazon-api'\n\nexport default function MarketAnalysis() {\n  const [niche, setNiche] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [analysis, setAnalysis] = useState<MarketAnalysisResult | null>(null)\n\n  const handleAnalyze = async () => {\n    if (!niche.trim()) return\n\n    setLoading(true)\n    try {\n      // First, search for products in the niche\n      const searchResults = await AmazonAPIService.searchProducts(niche, 1)\n      \n      if (searchResults.data.length === 0) {\n        throw new Error('No products found for this niche')\n      }\n\n      // Generate market analysis\n      const marketAnalysis = await OpenRouterAIService.generateMarketAnalysis(\n        searchResults.data.slice(0, 10), // Analyze top 10 products\n        niche\n      )\n      \n      setAnalysis(marketAnalysis)\n    } catch (error) {\n      console.error('Market analysis failed:', error)\n      // Handle error - show toast notification\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getCompetitionColor = (level: string) => {\n    switch (level) {\n      case 'low': return 'text-green-600 bg-green-100'\n      case 'medium': return 'text-yellow-600 bg-yellow-100'\n      case 'high': return 'text-red-600 bg-red-100'\n      default: return 'text-gray-600 bg-gray-100'\n    }\n  }\n\n  const getOpportunityColor = (score: number) => {\n    if (score >= 70) return 'text-green-600'\n    if (score >= 40) return 'text-yellow-600'\n    return 'text-red-600'\n  }\n\n  const getTrendIcon = (trend: string) => {\n    switch (trend) {\n      case 'increasing': return <TrendingUp className=\"w-4 h-4 text-green-600\" />\n      case 'decreasing': return <TrendingDown className=\"w-4 h-4 text-red-600\" />\n      default: return <BarChart3 className=\"w-4 h-4 text-gray-600\" />\n    }\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Market Analysis</h1>\n        <p className=\"text-gray-600 mt-2\">\n          Analyze market opportunities and competition for any niche on Amazon.\n        </p>\n      </div>\n\n      {/* Analysis Input */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Niche Market Analysis</CardTitle>\n          <CardDescription>\n            Enter a product niche or category to get comprehensive market insights\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex gap-4\">\n            <Input\n              placeholder=\"e.g., wireless earbuds, yoga mats, coffee makers...\"\n              value={niche}\n              onChange={(e) => setNiche(e.target.value)}\n              onKeyPress={(e) => e.key === 'Enter' && handleAnalyze()}\n              className=\"flex-1\"\n            />\n            <Button \n              onClick={handleAnalyze} \n              disabled={loading || !niche.trim()}\n              className=\"px-8\"\n            >\n              {loading ? (\n                <Loader2 className=\"w-4 h-4 animate-spin\" />\n              ) : (\n                <BarChart3 className=\"w-4 h-4\" />\n              )}\n              {loading ? 'Analyzing...' : 'Analyze Market'}\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Analysis Results */}\n      {analysis && (\n        <div className=\"space-y-6\">\n          {/* Market Overview */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Market Size</CardTitle>\n                <Package className=\"w-4 h-4 text-blue-400\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-lg font-bold\">{analysis.market_size}</div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Competition Level</CardTitle>\n                <Users className=\"w-4 h-4 text-purple-400\" />\n              </CardHeader>\n              <CardContent>\n                <div className={`text-lg font-bold capitalize px-3 py-1 rounded-full inline-block ${getCompetitionColor(analysis.competition_level)}`}>\n                  {analysis.competition_level}\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Opportunity Score</CardTitle>\n                <TrendingUp className=\"w-4 h-4 text-green-400\" />\n              </CardHeader>\n              <CardContent>\n                <div className={`text-2xl font-bold ${getOpportunityColor(analysis.opportunity_score)}`}>\n                  {analysis.opportunity_score}/100\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Average Price</CardTitle>\n                <DollarSign className=\"w-4 h-4 text-green-400\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">${analysis.price_analysis.average_price}</div>\n                <div className=\"flex items-center text-sm text-gray-600 mt-1\">\n                  {getTrendIcon(analysis.price_analysis.price_trend)}\n                  <span className=\"ml-1 capitalize\">{analysis.price_analysis.price_trend}</span>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Price Analysis */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Price Analysis</CardTitle>\n              <CardDescription>\n                Pricing insights and trends for the {niche} market\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid md:grid-cols-3 gap-6\">\n                <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-gray-900\">\n                    ${analysis.price_analysis.price_range.min}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">Minimum Price</div>\n                </div>\n                <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-blue-900\">\n                    ${analysis.price_analysis.average_price}\n                  </div>\n                  <div className=\"text-sm text-blue-600\">Average Price</div>\n                </div>\n                <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-gray-900\">\n                    ${analysis.price_analysis.price_range.max}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">Maximum Price</div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Key Insights */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Key Market Insights</CardTitle>\n              <CardDescription>\n                Important findings and observations about the {niche} market\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {analysis.key_insights.map((insight, index) => (\n                  <div key={index} className=\"flex items-start gap-3 p-3 bg-blue-50 rounded-lg\">\n                    <CheckCircle className=\"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0\" />\n                    <p className=\"text-blue-900\">{insight}</p>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Recommended Actions */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Recommended Actions</CardTitle>\n              <CardDescription>\n                Strategic recommendations based on the market analysis\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {analysis.recommended_actions.map((action, index) => (\n                  <div key={index} className=\"flex items-start gap-3 p-3 bg-green-50 rounded-lg\">\n                    <AlertCircle className=\"w-5 h-5 text-green-600 mt-0.5 flex-shrink-0\" />\n                    <p className=\"text-green-900\">{action}</p>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Empty State */}\n      {!analysis && !loading && (\n        <Card className=\"text-center py-12\">\n          <CardContent>\n            <BarChart3 className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n              No Analysis Yet\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Enter a product niche above to get comprehensive market insights and competitive analysis.\n            </p>\n            <div className=\"flex justify-center gap-4\">\n              <Button variant=\"outline\">\n                <Package className=\"w-4 h-4 mr-2\" />\n                View Examples\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AAlBA;;;;;;;;AAoBe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAEtE,MAAM,gBAAgB;QACpB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,WAAW;QACX,IAAI;YACF,0CAA0C;YAC1C,MAAM,gBAAgB,MAAM,0IAAA,CAAA,mBAAgB,CAAC,cAAc,CAAC,OAAO;YAEnE,IAAI,cAAc,IAAI,CAAC,MAAM,KAAK,GAAG;gBACnC,MAAM,IAAI,MAAM;YAClB;YAEA,2BAA2B;YAC3B,MAAM,iBAAiB,MAAM,6IAAA,CAAA,sBAAmB,CAAC,sBAAsB,CACrE,cAAc,IAAI,CAAC,KAAK,CAAC,GAAG,KAC5B;YAGF,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,yCAAyC;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAc,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAc,qBAAO,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YAClD;gBAAS,qBAAO,6LAAC,qNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QACvC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAMpC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;oCACxC,WAAU;;;;;;8CAEZ,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,WAAW,CAAC,MAAM,IAAI;oCAChC,WAAU;;wCAET,wBACC,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAEtB,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;YAOnC,0BACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;kDAErB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDAAqB,SAAS,WAAW;;;;;;;;;;;;;;;;;0CAI5D,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAW,CAAC,iEAAiE,EAAE,oBAAoB,SAAS,iBAAiB,GAAG;sDAClI,SAAS,iBAAiB;;;;;;;;;;;;;;;;;0CAKjC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAW,CAAC,mBAAmB,EAAE,oBAAoB,SAAS,iBAAiB,GAAG;;gDACpF,SAAS,iBAAiB;gDAAC;;;;;;;;;;;;;;;;;;0CAKlC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;;oDAAqB;oDAAE,SAAS,cAAc,CAAC,aAAa;;;;;;;0DAC3E,6LAAC;gDAAI,WAAU;;oDACZ,aAAa,SAAS,cAAc,CAAC,WAAW;kEACjD,6LAAC;wDAAK,WAAU;kEAAmB,SAAS,cAAc,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9E,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;;4CAAC;4CACsB;4CAAM;;;;;;;;;;;;;0CAG/C,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDAAmC;wDAC9C,SAAS,cAAc,CAAC,WAAW,CAAC,GAAG;;;;;;;8DAE3C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDAAmC;wDAC9C,SAAS,cAAc,CAAC,aAAa;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDAAmC;wDAC9C,SAAS,cAAc,CAAC,WAAW,CAAC,GAAG;;;;;;;8DAE3C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/C,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;;4CAAC;4CACgC;4CAAM;;;;;;;;;;;;;0CAGzD,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,SAAS,YAAY,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnC,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAE,WAAU;8DAAiB;;;;;;;2CAFtB;;;;;;;;;;;;;;;;;;;;;kCAUlB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,SAAS,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzC,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAE,WAAU;8DAAkB;;;;;;;2CAFvB;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYrB,CAAC,YAAY,CAAC,yBACb,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;;sCACV,6LAAC,qNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpD;GAxPwB;KAAA", "debugId": null}}]}