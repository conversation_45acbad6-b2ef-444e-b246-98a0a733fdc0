{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/app/api/amazon/product/%5Basin%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport axios from 'axios'\n\nconst RAPIDAPI_KEY = process.env.RAPIDAPI_KEY!\nconst BASE_URL = 'https://real-time-amazon-data.p.rapidapi.com'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ asin: string }> }\n) {\n  try {\n    const { asin } = await params\n    const { searchParams } = new URL(request.url)\n    const country = searchParams.get('country') || 'US'\n\n    if (!asin) {\n      return NextResponse.json(\n        { error: 'ASIN parameter is required' },\n        { status: 400 }\n      )\n    }\n\n    const response = await axios.get(`${BASE_URL}/product-details`, {\n      params: {\n        asin,\n        country\n      },\n      headers: {\n        'x-rapidapi-key': RAPIDAPI_KEY,\n        'x-rapidapi-host': 'real-time-amazon-data.p.rapidapi.com'\n      }\n    })\n\n    return NextResponse.json(response.data)\n  } catch (error) {\n    console.error('Amazon product API error:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch product details' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,eAAe,QAAQ,GAAG,CAAC,YAAY;AAC7C,MAAM,WAAW;AAEV,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAyC;IAEjD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;QACvB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,UAAU,aAAa,GAAG,CAAC,cAAc;QAE/C,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,SAAS,gBAAgB,CAAC,EAAE;YAC9D,QAAQ;gBACN;gBACA;YACF;YACA,SAAS;gBACP,kBAAkB;gBAClB,mBAAmB;YACrB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,SAAS,IAAI;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}