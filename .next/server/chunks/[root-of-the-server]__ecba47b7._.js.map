{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/lib/services/openrouter-ai.ts"], "sourcesContent": ["const API_KEY = process.env.OPENROUTER_API_KEY!\n\nexport interface ReviewAnalysisResult {\n  pros: Array<{ text: string; count: number }>\n  cons: Array<{ text: string; count: number }>\n  sentiment_score?: number\n  summary?: string\n}\n\nexport interface MarketAnalysisResult {\n  market_size: string\n  competition_level: 'low' | 'medium' | 'high'\n  opportunity_score: number\n  key_insights: string[]\n  recommended_actions: string[]\n  price_analysis: {\n    average_price: number\n    price_range: { min: number; max: number }\n    price_trend: 'increasing' | 'decreasing' | 'stable'\n  }\n}\n\nexport class OpenRouterAIService {\n  static async analyzeReviews(\n    reviews: string[],\n    locale: 'en' | 'fr' = 'en'\n  ): Promise<ReviewAnalysisResult> {\n    try {\n      const response = await fetch('/api/ai/analyze-reviews', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ reviews, locale })\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to analyze reviews')\n      }\n\n      return await response.json()\n    } catch (error) {\n      console.error('Error analyzing reviews:', error)\n      throw new Error('Failed to analyze reviews')\n    }\n  }\n\n  static async generateMarketAnalysis(\n    products: any[],\n    niche: string\n  ): Promise<MarketAnalysisResult> {\n    try {\n      const response = await fetch('/api/ai/market-analysis', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ products, niche })\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to generate market analysis')\n      }\n\n      return await response.json()\n    } catch (error) {\n      console.error('Error generating market analysis:', error)\n      throw new Error('Failed to generate market analysis')\n    }\n  }\n\n  static async generateProductInsights(\n    productDetails: any,\n    reviews: string[]\n  ): Promise<{\n    strengths: string[]\n    weaknesses: string[]\n    opportunities: string[]\n    threats: string[]\n    recommendations: string[]\n  }> {\n    try {\n      const requestBody = {\n        model: \"deepseek/deepseek-r1:free\",\n        messages: [{\n          role: \"user\",\n          content: `Analyze this product and its reviews to provide strategic insights:\n\n                   Product: ${productDetails.product_title}\n                   Price: ${productDetails.product_price}\n                   Rating: ${productDetails.product_star_rating}\n                   Reviews Count: ${productDetails.product_num_ratings}\n\n                   Sample Reviews: ${reviews.slice(0, 50).join('\\n')}\n\n                   Provide a SWOT analysis and recommendations in JSON format:\n                   {\n                     \"strengths\": [\"strength1\", \"strength2\", \"strength3\"],\n                     \"weaknesses\": [\"weakness1\", \"weakness2\", \"weakness3\"],\n                     \"opportunities\": [\"opportunity1\", \"opportunity2\", \"opportunity3\"],\n                     \"threats\": [\"threat1\", \"threat2\", \"threat3\"],\n                     \"recommendations\": [\"rec1\", \"rec2\", \"rec3\"]\n                   }\n\n                   IMPORTANT: Respond ONLY with valid JSON, no markdown or backticks.`\n        }]\n      }\n\n      const response = await fetch(\"https://openrouter.ai/api/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Authorization\": `Bearer ${API_KEY}`,\n          \"HTTP-Referer\": \"https://github.com/amazon-product-insights\",\n          \"X-Title\": \"Amazon Product Insights\",\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(requestBody)\n      })\n\n      if (!response.ok) {\n        throw new Error(`API request failed: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n      const content = data.choices[0]?.message?.content\n\n      if (!content) {\n        throw new Error('No content received from AI service')\n      }\n\n      // Clean the response and parse JSON\n      const cleanedContent = content.replace(/```json|```/g, '').trim()\n      return JSON.parse(cleanedContent)\n    } catch (error) {\n      console.error('Error generating product insights:', error)\n      throw new Error('Failed to generate product insights')\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,UAAU,QAAQ,GAAG,CAAC,kBAAkB;AAsBvC,MAAM;IACX,aAAa,eACX,OAAiB,EACjB,SAAsB,IAAI,EACK;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAS;gBAAO;YACzC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,uBACX,QAAe,EACf,KAAa,EACkB;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAU;gBAAM;YACzC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,wBACX,cAAmB,EACnB,OAAiB,EAOhB;QACD,IAAI;YACF,MAAM,cAAc;gBAClB,OAAO;gBACP,UAAU;oBAAC;wBACT,MAAM;wBACN,SAAS,CAAC;;4BAEQ,EAAE,eAAe,aAAa,CAAC;0BACjC,EAAE,eAAe,aAAa,CAAC;2BAC9B,EAAE,eAAe,mBAAmB,CAAC;kCAC9B,EAAE,eAAe,mBAAmB,CAAC;;mCAEpC,EAAE,QAAQ,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM;;;;;;;;;;;qFAWgB,CAAC;oBAC9E;iBAAE;YACJ;YAEA,MAAM,WAAW,MAAM,MAAM,iDAAiD;gBAC5E,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,SAAS;oBACpC,gBAAgB;oBAChB,WAAW;oBACX,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,UAAU,EAAE;YAC9D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,UAAU,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS;YAE1C,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,MAAM,iBAAiB,QAAQ,OAAO,CAAC,gBAAgB,IAAI,IAAI;YAC/D,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/app/api/ai/market-analysis/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { OpenRouterAIService } from '@/lib/services/openrouter-ai'\n\nconst API_KEY = process.env.OPENROUTER_API_KEY!\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { products, niche } = body\n\n    console.log('🤖 Market analysis request received for niche:', niche)\n    console.log('📊 Products count:', products?.length)\n\n    if (!products || !Array.isArray(products) || products.length === 0) {\n      return NextResponse.json(\n        { error: 'Products array is required and must not be empty' },\n        { status: 400 }\n      )\n    }\n\n    if (!niche || typeof niche !== 'string') {\n      return NextResponse.json(\n        { error: 'Niche parameter is required and must be a string' },\n        { status: 400 }\n      )\n    }\n\n    // If no API key, return mock analysis\n    if (!API_KEY || API_KEY === 'your_openrouter_api_key_here') {\n      console.log('⚠️ No valid OpenRouter API key found, returning mock analysis')\n      const mockAnalysis = {\n        market_size: `Large market with ${products.length * 100}+ products`,\n        competition_level: products.length > 5 ? 'high' : products.length > 2 ? 'medium' : 'low',\n        opportunity_score: Math.floor(Math.random() * 40) + 60, // 60-100\n        key_insights: [\n          `The ${niche} market shows strong demand with consistent sales`,\n          `Price points range from $${Math.min(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))} to $${Math.max(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))}`,\n          `Customer satisfaction is generally high with average ratings above 4.0`\n        ],\n        recommended_actions: [\n          `Focus on products in the $${Math.floor(Math.random() * 20) + 20}-$${Math.floor(Math.random() * 30) + 40} price range`,\n          `Emphasize quality and customer service to compete effectively`,\n          `Consider seasonal trends and inventory management for ${niche} products`\n        ],\n        price_analysis: {\n          average_price: Math.floor(products.reduce((sum, p) => sum + parseFloat(p.product_price?.replace('$', '') || '0'), 0) / products.length),\n          price_range: {\n            min: Math.min(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0'))),\n            max: Math.max(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))\n          },\n          price_trend: ['increasing', 'decreasing', 'stable'][Math.floor(Math.random() * 3)]\n        }\n      }\n      return NextResponse.json(mockAnalysis)\n    }\n\n    const analysis = await OpenRouterAIService.generateMarketAnalysis(products, niche)\n\n    return NextResponse.json(analysis)\n  } catch (error) {\n    console.error('Market analysis API error:', error)\n    return NextResponse.json(\n      { error: 'Failed to generate market analysis' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,kBAAkB;AAEvC,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;QAE5B,QAAQ,GAAG,CAAC,kDAAkD;QAC9D,QAAQ,GAAG,CAAC,sBAAsB,UAAU;QAE5C,IAAI,CAAC,YAAY,CAAC,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,KAAK,GAAG;YAClE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmD,GAC5D;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmD,GAC5D;gBAAE,QAAQ;YAAI;QAElB;QAEA,sCAAsC;QACtC,IAAI,CAAC,WAAW,YAAY,gCAAgC;YAC1D,QAAQ,GAAG,CAAC;YACZ,MAAM,eAAe;gBACnB,aAAa,CAAC,kBAAkB,EAAE,SAAS,MAAM,GAAG,IAAI,UAAU,CAAC;gBACnE,mBAAmB,SAAS,MAAM,GAAG,IAAI,SAAS,SAAS,MAAM,GAAG,IAAI,WAAW;gBACnF,mBAAmB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBACpD,cAAc;oBACZ,CAAC,IAAI,EAAE,MAAM,iDAAiD,CAAC;oBAC/D,CAAC,yBAAyB,EAAE,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,WAAW,EAAE,aAAa,EAAE,QAAQ,KAAK,OAAO,OAAO,KAAK,EAAE,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,WAAW,EAAE,aAAa,EAAE,QAAQ,KAAK,OAAO,QAAQ;oBAC9M,CAAC,sEAAsE,CAAC;iBACzE;gBACD,qBAAqB;oBACnB,CAAC,0BAA0B,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,EAAE,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,YAAY,CAAC;oBACtH,CAAC,6DAA6D,CAAC;oBAC/D,CAAC,sDAAsD,EAAE,MAAM,SAAS,CAAC;iBAC1E;gBACD,gBAAgB;oBACd,eAAe,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,WAAW,EAAE,aAAa,EAAE,QAAQ,KAAK,OAAO,MAAM,KAAK,SAAS,MAAM;oBACtI,aAAa;wBACX,KAAK,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,WAAW,EAAE,aAAa,EAAE,QAAQ,KAAK,OAAO;wBACnF,KAAK,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,WAAW,EAAE,aAAa,EAAE,QAAQ,KAAK,OAAO;oBACrF;oBACA,aAAa;wBAAC;wBAAc;wBAAc;qBAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;gBACpF;YACF;YACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,MAAM,WAAW,MAAM,4IAAA,CAAA,sBAAmB,CAAC,sBAAsB,CAAC,UAAU;QAE5E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqC,GAC9C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}