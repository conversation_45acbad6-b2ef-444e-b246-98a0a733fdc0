{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/app/api/ai/analyze-reviews/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nconst API_KEY = process.env.OPENROUTER_API_KEY!\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { reviews, locale = 'en' } = body\n\n    console.log('🤖 Review analysis request received')\n    console.log('📝 Reviews count:', reviews?.length)\n\n    if (!reviews || !Array.isArray(reviews) || reviews.length === 0) {\n      return NextResponse.json(\n        { error: 'Reviews array is required and must not be empty' },\n        { status: 400 }\n      )\n    }\n\n    // If no API key, return mock analysis\n    if (!API_KEY || API_KEY === 'your_openrouter_api_key_here') {\n      console.log('⚠️ No valid OpenRouter API key found, returning mock review analysis')\n\n      // Generate mock analysis based on review content\n      const mockAnalysis = {\n        pros: [\n          { text: 'Easy installation/setup', count: Math.floor(reviews.length * 0.4) },\n          { text: 'Good screen size/quality', count: Math.floor(reviews.length * 0.3) },\n          { text: 'Modernizes older vehicles with CarPlay/Android Auto', count: Math.floor(reviews.length * 0.25) }\n        ],\n        cons: [\n          { text: 'Audio quality issues (tinny sound, low-end loss)', count: Math.floor(reviews.length * 0.3) },\n          { text: 'Device randomly restarts after extended use', count: Math.floor(reviews.length * 0.2) },\n          { text: 'Laggy touchscreen responsiveness', count: Math.floor(reviews.length * 0.15) }\n        ],\n        sentiment_score: 0.6,\n        summary: `Based on ${reviews.length} reviews, customers appreciate the ease of installation and screen quality, but report some audio and reliability issues. Overall sentiment is positive with room for improvement.`\n      }\n\n      return NextResponse.json(mockAnalysis)\n    }\n\n    console.log('🚀 Making request to OpenRouter API for review analysis...')\n    const requestBody = {\n      model: \"deepseek/deepseek-r1:free\",\n      messages: [{\n        role: \"user\",\n        content: `Analyze these reviews and identify the most mentioned positive (pros) and negative (cons) points.\n                 Respond ONLY in JSON with this structure:\n                 {\"pros\":[{\"text\":\"point\",\"count\":n}],\"cons\":[{\"text\":\"point\",\"count\":n}],\"sentiment_score\":0.0}\n                 Limit to 3 points per category, in ${locale === 'fr' ? 'French' : 'English'}.\n                 IMPORTANT: Do not include backticks or markdown markers in the response.\n                 Sentiment score should be between -1 (very negative) and 1 (very positive).\n                 Reviews: ${reviews.slice(0, 200).join('\\n')}`\n      }]\n    }\n\n    const response = await fetch(\"https://openrouter.ai/api/v1/chat/completions\", {\n      method: \"POST\",\n      headers: {\n        \"Authorization\": `Bearer ${API_KEY}`,\n        \"HTTP-Referer\": \"https://github.com/amazon-review-insights\",\n        \"X-Title\": \"Amazon Review Insights\",\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify(requestBody)\n    })\n\n    if (!response.ok) {\n      throw new Error(`API request failed: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    const content = data.choices[0]?.message?.content\n\n    if (!content) {\n      throw new Error('No content received from AI service')\n    }\n\n    // Clean the response and parse JSON\n    const cleanedContent = content.replace(/```json|```/g, '').trim()\n    const analysis = JSON.parse(cleanedContent)\n\n    return NextResponse.json(analysis)\n  } catch (error) {\n    console.error('Review analysis API error:', error)\n    return NextResponse.json(\n      { error: 'Failed to analyze reviews' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,kBAAkB;AAEvC,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,SAAS,IAAI,EAAE,GAAG;QAEnC,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,qBAAqB,SAAS;QAE1C,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,KAAK,GAAG;YAC/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkD,GAC3D;gBAAE,QAAQ;YAAI;QAElB;QAEA,sCAAsC;QACtC,IAAI,CAAC,WAAW,YAAY,gCAAgC;YAC1D,QAAQ,GAAG,CAAC;YAEZ,iDAAiD;YACjD,MAAM,eAAe;gBACnB,MAAM;oBACJ;wBAAE,MAAM;wBAA2B,OAAO,KAAK,KAAK,CAAC,QAAQ,MAAM,GAAG;oBAAK;oBAC3E;wBAAE,MAAM;wBAA4B,OAAO,KAAK,KAAK,CAAC,QAAQ,MAAM,GAAG;oBAAK;oBAC5E;wBAAE,MAAM;wBAAuD,OAAO,KAAK,KAAK,CAAC,QAAQ,MAAM,GAAG;oBAAM;iBACzG;gBACD,MAAM;oBACJ;wBAAE,MAAM;wBAAoD,OAAO,KAAK,KAAK,CAAC,QAAQ,MAAM,GAAG;oBAAK;oBACpG;wBAAE,MAAM;wBAA+C,OAAO,KAAK,KAAK,CAAC,QAAQ,MAAM,GAAG;oBAAK;oBAC/F;wBAAE,MAAM;wBAAoC,OAAO,KAAK,KAAK,CAAC,QAAQ,MAAM,GAAG;oBAAM;iBACtF;gBACD,iBAAiB;gBACjB,SAAS,CAAC,SAAS,EAAE,QAAQ,MAAM,CAAC,kLAAkL,CAAC;YACzN;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,QAAQ,GAAG,CAAC;QACZ,MAAM,cAAc;YAClB,OAAO;YACP,UAAU;gBAAC;oBACT,MAAM;oBACN,SAAS,CAAC;;;oDAGkC,EAAE,WAAW,OAAO,WAAW,UAAU;;;0BAGnE,EAAE,QAAQ,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO;gBACxD;aAAE;QACJ;QAEA,MAAM,WAAW,MAAM,MAAM,iDAAiD;YAC5E,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,SAAS;gBACpC,gBAAgB;gBAChB,WAAW;gBACX,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,UAAU,EAAE;QAC9D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,UAAU,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS;QAE1C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,oCAAoC;QACpC,MAAM,iBAAiB,QAAQ,OAAO,CAAC,gBAAgB,IAAI,IAAI;QAC/D,MAAM,WAAW,KAAK,KAAK,CAAC;QAE5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}