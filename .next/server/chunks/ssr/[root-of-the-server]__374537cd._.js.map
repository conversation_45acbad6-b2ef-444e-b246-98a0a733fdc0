{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/lib/services/amazon-api.ts"], "sourcesContent": ["import axios from 'axios'\n\nconst RAPIDAPI_KEY = process.env.RAPIDAPI_KEY!\nconst BASE_URL = 'https://real-time-amazon-data.p.rapidapi.com'\n\nconst apiClient = axios.create({\n  baseURL: BASE_URL,\n  headers: {\n    'x-rapidapi-key': RAPIDAPI_KEY,\n    'x-rapidapi-host': 'real-time-amazon-data.p.rapidapi.com'\n  }\n})\n\nexport interface AmazonProduct {\n  asin: string\n  product_title: string\n  product_price: string\n  product_original_price?: string\n  currency: string\n  product_star_rating: string\n  product_num_ratings: number\n  product_url: string\n  product_photo: string\n  product_availability: string\n  is_best_seller: boolean\n  is_amazon_choice: boolean\n  is_prime: boolean\n  climate_pledge_friendly: boolean\n  sales_volume?: string\n  delivery?: string\n}\n\nexport interface AmazonReview {\n  review_id: string\n  review_title: string\n  review_comment: string\n  review_star_rating: number\n  review_date: string\n  review_author: string\n  review_author_avatar?: string\n  is_verified_purchase: boolean\n  helpful_vote_statement?: string\n  review_country: string\n  review_images?: string[]\n  review_videos?: string[]\n}\n\nexport interface ProductDetails {\n  asin: string\n  product_title: string\n  product_price: string\n  product_original_price?: string\n  currency: string\n  product_star_rating: string\n  product_num_ratings: number\n  product_url: string\n  product_photos: string[]\n  product_details: Record<string, any>\n  product_description: string\n  product_information: Record<string, any>\n  product_specifications: Record<string, any>\n  customers_say: {\n    keywords: Array<{\n      keyword: string\n      sentiment_score: number\n      mentions_count: number\n    }>\n  }\n  category_tree: Array<{\n    name: string\n    link: string\n  }>\n}\n\nexport class AmazonAPIService {\n  static async searchProducts(query: string, page = 1, country = 'US'): Promise<{\n    data: AmazonProduct[]\n    total_products: number\n    country: string\n    domain: string\n  }> {\n    try {\n      const response = await fetch(`/api/amazon/search?query=${encodeURIComponent(query)}&page=${page}&country=${country}`)\n      if (!response.ok) {\n        throw new Error('Failed to search products')\n      }\n      return await response.json()\n    } catch (error) {\n      console.error('Error searching products:', error)\n      throw new Error('Failed to search products')\n    }\n  }\n\n  static async getProductDetails(asin: string, country = 'US'): Promise<ProductDetails> {\n    try {\n      const response = await fetch(`/api/amazon/product/${asin}?country=${country}`)\n      if (!response.ok) {\n        throw new Error('Failed to fetch product details')\n      }\n      return await response.json()\n    } catch (error) {\n      console.error('Error fetching product details:', error)\n      throw new Error('Failed to fetch product details')\n    }\n  }\n\n  static async getProductReviews(\n    asin: string,\n    page = 1,\n    country = 'US',\n    sortBy = 'TOP_REVIEWS'\n  ): Promise<{\n    data: AmazonReview[]\n    total_reviews: number\n    country: string\n    domain: string\n  }> {\n    try {\n      const response = await fetch(`/api/amazon/reviews/${asin}?page=${page}&country=${country}&sortBy=${sortBy}`)\n      if (!response.ok) {\n        throw new Error('Failed to fetch product reviews')\n      }\n      return await response.json()\n    } catch (error) {\n      console.error('Error fetching product reviews:', error)\n      throw new Error('Failed to fetch product reviews')\n    }\n  }\n\n  static async getProductsByCategory(\n    categoryId: string,\n    page = 1,\n    country = 'US'\n  ): Promise<{\n    data: AmazonProduct[]\n    total_products: number\n    country: string\n    domain: string\n  }> {\n    try {\n      const response = await apiClient.get('/products-by-category', {\n        params: {\n          category_id: categoryId,\n          page: page.toString(),\n          country\n        }\n      })\n      return response.data\n    } catch (error) {\n      console.error('Error fetching products by category:', error)\n      throw new Error('Failed to fetch products by category')\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,eAAe,QAAQ,GAAG,CAAC,YAAY;AAC7C,MAAM,WAAW;AAEjB,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,kBAAkB;QAClB,mBAAmB;IACrB;AACF;AA+DO,MAAM;IACX,aAAa,eAAe,KAAa,EAAE,OAAO,CAAC,EAAE,UAAU,IAAI,EAKhE;QACD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,yBAAyB,EAAE,mBAAmB,OAAO,MAAM,EAAE,KAAK,SAAS,EAAE,SAAS;YACpH,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,kBAAkB,IAAY,EAAE,UAAU,IAAI,EAA2B;QACpF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,KAAK,SAAS,EAAE,SAAS;YAC7E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,kBACX,IAAY,EACZ,OAAO,CAAC,EACR,UAAU,IAAI,EACd,SAAS,aAAa,EAMrB;QACD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,KAAK,MAAM,EAAE,KAAK,SAAS,EAAE,QAAQ,QAAQ,EAAE,QAAQ;YAC3G,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,sBACX,UAAkB,EAClB,OAAO,CAAC,EACR,UAAU,IAAI,EAMb;QACD,IAAI;YACF,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,yBAAyB;gBAC5D,QAAQ;oBACN,aAAa;oBACb,MAAM,KAAK,QAAQ;oBACnB;gBACF;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/lib/services/openrouter-ai.ts"], "sourcesContent": ["const API_KEY = process.env.OPENROUTER_API_KEY!\n\nexport interface ReviewAnalysisResult {\n  pros: Array<{ text: string; count: number }>\n  cons: Array<{ text: string; count: number }>\n  sentiment_score?: number\n  summary?: string\n}\n\nexport interface MarketAnalysisResult {\n  market_size: string\n  competition_level: 'low' | 'medium' | 'high'\n  opportunity_score: number\n  key_insights: string[]\n  recommended_actions: string[]\n  price_analysis: {\n    average_price: number\n    price_range: { min: number; max: number }\n    price_trend: 'increasing' | 'decreasing' | 'stable'\n    profit_margins: {\n      low_end: number\n      high_end: number\n      average: number\n    }\n  }\n  competitor_analysis: {\n    top_brands: string[]\n    market_leaders: {\n      brand: string\n      market_share: number\n      key_advantages: string[]\n    }[]\n    competitive_gaps: string[]\n  }\n  customer_analysis: {\n    target_demographics: string[]\n    buying_patterns: string[]\n    pain_points: string[]\n    satisfaction_level: number\n  }\n  market_trends: {\n    seasonal_patterns: string\n    growth_trajectory: 'growing' | 'stable' | 'declining'\n    emerging_features: string[]\n    market_maturity: 'emerging' | 'growing' | 'mature' | 'declining'\n  }\n  risk_assessment: {\n    market_risks: string[]\n    competitive_threats: string[]\n    opportunity_risks: string[]\n    risk_level: 'low' | 'medium' | 'high'\n  }\n  entry_strategy: {\n    recommended_price_point: {\n      min: number\n      max: number\n      optimal: number\n    }\n    differentiation_opportunities: string[]\n    marketing_channels: string[]\n    timeline_to_market: string\n  }\n  top_products: Array<{\n    asin: string\n    title: string\n    price: string\n    rating: string\n    reviews: number\n    sales_volume: string\n    is_best_seller: boolean\n    competitive_advantage: string\n    market_position: 'leader' | 'challenger' | 'follower' | 'niche'\n  }>\n}\n\nexport class OpenRouterAIService {\n  static async analyzeReviews(\n    reviews: string[],\n    locale: 'en' | 'fr' = 'en'\n  ): Promise<ReviewAnalysisResult> {\n    try {\n      const response = await fetch('/api/ai/analyze-reviews', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ reviews, locale })\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to analyze reviews')\n      }\n\n      return await response.json()\n    } catch (error) {\n      console.error('Error analyzing reviews:', error)\n      throw new Error('Failed to analyze reviews')\n    }\n  }\n\n  static async generateMarketAnalysis(\n    products: any[],\n    niche: string\n  ): Promise<MarketAnalysisResult> {\n    try {\n      console.log('🤖 Generating market analysis with OpenRouter AI for niche:', niche)\n      console.log('📊 Products to analyze:', products.length)\n\n      // If no API key, return mock analysis\n      if (!API_KEY || API_KEY === 'your_openrouter_api_key_here') {\n        console.log('⚠️ No valid OpenRouter API key found, returning mock analysis')\n        const competitionLevel = products.length > 5 ? 'high' : products.length > 2 ? 'medium' : 'low'\n        const priceTrend = ['increasing', 'decreasing', 'stable'][Math.floor(Math.random() * 3)] as 'increasing' | 'decreasing' | 'stable'\n\n        const avgPrice = Math.floor(products.reduce((sum, p) => sum + parseFloat(p.product_price?.replace('$', '') || '0'), 0) / products.length)\n        const minPrice = Math.min(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))\n        const maxPrice = Math.max(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))\n\n        const mockAnalysis: MarketAnalysisResult = {\n          market_size: `Large market with ${products.length * 100}+ products and $${Math.floor(avgPrice * products.length * 1000)}M+ annual revenue`,\n          competition_level: competitionLevel as 'low' | 'medium' | 'high',\n          opportunity_score: Math.floor(Math.random() * 40) + 60, // 60-100\n          key_insights: [\n            `The ${niche} market shows strong demand with consistent sales across ${products.length} analyzed products`,\n            `Price points range from $${minPrice} to $${maxPrice} with healthy profit margins`,\n            `Customer satisfaction is generally high with average ratings above 4.0 stars`,\n            `Market shows ${priceTrend} price trends indicating ${priceTrend === 'increasing' ? 'growing demand' : priceTrend === 'decreasing' ? 'price competition' : 'market stability'}`,\n            `Best-selling products dominate with ${products.filter(p => p.is_best_seller).length} out of ${products.length} products having best-seller status`\n          ],\n          recommended_actions: [\n            `Target the $${Math.floor(avgPrice * 0.8)}-$${Math.floor(avgPrice * 1.2)} price range for optimal market entry`,\n            `Focus on quality and customer service to compete with established brands`,\n            `Leverage emerging features and customer pain points for differentiation`,\n            `Consider seasonal trends and inventory management for ${niche} products`,\n            `Build strong brand presence through targeted marketing channels`\n          ],\n          price_analysis: {\n            average_price: avgPrice,\n            price_range: { min: minPrice, max: maxPrice },\n            price_trend: priceTrend,\n            profit_margins: {\n              low_end: Math.floor(minPrice * 0.3),\n              high_end: Math.floor(maxPrice * 0.6),\n              average: Math.floor(avgPrice * 0.45)\n            }\n          },\n          competitor_analysis: {\n            top_brands: ['Apple', 'Samsung', 'Sony', 'JBL', 'Anker'].slice(0, 3),\n            market_leaders: [\n              {\n                brand: 'Market Leader',\n                market_share: 35,\n                key_advantages: ['Brand recognition', 'Distribution network', 'Product quality']\n              },\n              {\n                brand: 'Strong Challenger',\n                market_share: 25,\n                key_advantages: ['Competitive pricing', 'Innovation', 'Customer service']\n              }\n            ],\n            competitive_gaps: [\n              'Limited options in mid-range pricing',\n              'Lack of eco-friendly alternatives',\n              'Poor customer support in some segments'\n            ]\n          },\n          customer_analysis: {\n            target_demographics: ['Tech enthusiasts', 'Professionals', 'Budget-conscious consumers'],\n            buying_patterns: ['Research-driven purchases', 'Price comparison shopping', 'Review-influenced decisions'],\n            pain_points: ['Complex setup', 'Durability concerns', 'Limited warranty coverage'],\n            satisfaction_level: 4.2\n          },\n          market_trends: {\n            seasonal_patterns: 'Peak sales during Q4 holidays and back-to-school season',\n            growth_trajectory: 'growing' as const,\n            emerging_features: ['Wireless connectivity', 'AI integration', 'Sustainability focus'],\n            market_maturity: 'growing' as const\n          },\n          risk_assessment: {\n            market_risks: ['Economic downturn impact', 'Supply chain disruptions', 'Technology obsolescence'],\n            competitive_threats: ['New market entrants', 'Price wars', 'Patent disputes'],\n            opportunity_risks: ['Market saturation', 'Changing consumer preferences', 'Regulatory changes'],\n            risk_level: competitionLevel as 'low' | 'medium' | 'high'\n          },\n          entry_strategy: {\n            recommended_price_point: {\n              min: Math.floor(avgPrice * 0.8),\n              max: Math.floor(avgPrice * 1.2),\n              optimal: avgPrice\n            },\n            differentiation_opportunities: [\n              'Superior customer support',\n              'Innovative features',\n              'Competitive pricing',\n              'Better user experience'\n            ],\n            marketing_channels: ['Amazon PPC', 'Social media advertising', 'Influencer partnerships', 'Content marketing'],\n            timeline_to_market: '3-6 months for product development and launch'\n          },\n          top_products: products.slice(0, 20).map((product, index) => ({\n            asin: product.asin,\n            title: product.product_title,\n            price: product.product_price || 'N/A',\n            rating: product.product_star_rating,\n            reviews: product.product_num_ratings,\n            sales_volume: product.sales_volume || 'N/A',\n            is_best_seller: product.is_best_seller || false,\n            competitive_advantage: index < 5 ? 'Market leader with strong brand' : index < 10 ? 'Strong challenger with good value' : index < 15 ? 'Solid follower with niche appeal' : 'Emerging player with potential',\n            market_position: (index < 3 ? 'leader' : index < 8 ? 'challenger' : index < 15 ? 'follower' : 'niche') as 'leader' | 'challenger' | 'follower' | 'niche'\n          }))\n        }\n        return mockAnalysis\n      }\n\n      // Prepare product data for AI analysis\n      const productSummary = products.slice(0, 10).map(p => ({\n        title: p.product_title,\n        price: p.product_price,\n        rating: p.product_star_rating,\n        reviews: p.product_num_ratings,\n        is_best_seller: p.is_best_seller,\n        sales_volume: p.sales_volume\n      }))\n\n      const requestBody = {\n        model: \"deepseek/deepseek-r1:free\",\n        messages: [{\n          role: \"user\",\n          content: `Analyze the ${niche} market based on these Amazon products and provide comprehensive market insights:\n\nProducts Data:\n${JSON.stringify(productSummary, null, 2)}\n\nProvide a detailed market analysis in JSON format with ALL the following fields:\n{\n  \"market_size\": \"detailed description of market size with revenue estimates\",\n  \"competition_level\": \"low|medium|high\",\n  \"opportunity_score\": number_between_0_and_100,\n  \"key_insights\": [\"insight1\", \"insight2\", \"insight3\", \"insight4\", \"insight5\"],\n  \"recommended_actions\": [\"action1\", \"action2\", \"action3\", \"action4\", \"action5\"],\n  \"price_analysis\": {\n    \"average_price\": number,\n    \"price_range\": {\"min\": number, \"max\": number},\n    \"price_trend\": \"increasing|decreasing|stable\",\n    \"profit_margins\": {\n      \"low_end\": number,\n      \"high_end\": number,\n      \"average\": number\n    }\n  },\n  \"competitor_analysis\": {\n    \"top_brands\": [\"brand1\", \"brand2\", \"brand3\"],\n    \"market_leaders\": [\n      {\n        \"brand\": \"brand_name\",\n        \"market_share\": number,\n        \"key_advantages\": [\"advantage1\", \"advantage2\", \"advantage3\"]\n      }\n    ],\n    \"competitive_gaps\": [\"gap1\", \"gap2\", \"gap3\"]\n  },\n  \"customer_analysis\": {\n    \"target_demographics\": [\"demo1\", \"demo2\", \"demo3\"],\n    \"buying_patterns\": [\"pattern1\", \"pattern2\", \"pattern3\"],\n    \"pain_points\": [\"pain1\", \"pain2\", \"pain3\"],\n    \"satisfaction_level\": number_between_1_and_5\n  },\n  \"market_trends\": {\n    \"seasonal_patterns\": \"description of seasonal trends\",\n    \"growth_trajectory\": \"growing|stable|declining\",\n    \"emerging_features\": [\"feature1\", \"feature2\", \"feature3\"],\n    \"market_maturity\": \"emerging|growing|mature|declining\"\n  },\n  \"risk_assessment\": {\n    \"market_risks\": [\"risk1\", \"risk2\", \"risk3\"],\n    \"competitive_threats\": [\"threat1\", \"threat2\", \"threat3\"],\n    \"opportunity_risks\": [\"risk1\", \"risk2\", \"risk3\"],\n    \"risk_level\": \"low|medium|high\"\n  },\n  \"entry_strategy\": {\n    \"recommended_price_point\": {\n      \"min\": number,\n      \"max\": number,\n      \"optimal\": number\n    },\n    \"differentiation_opportunities\": [\"opp1\", \"opp2\", \"opp3\"],\n    \"marketing_channels\": [\"channel1\", \"channel2\", \"channel3\"],\n    \"timeline_to_market\": \"timeline description\"\n  },\n  \"top_products\": [\n    {\n      \"asin\": \"product_asin\",\n      \"title\": \"product_title\",\n      \"price\": \"product_price\",\n      \"rating\": \"product_rating\",\n      \"reviews\": number_of_reviews,\n      \"sales_volume\": \"sales_volume\",\n      \"is_best_seller\": boolean,\n      \"competitive_advantage\": \"advantage description\",\n      \"market_position\": \"leader|challenger|follower|niche\"\n    }\n  ]\n}\n\nIMPORTANT: Respond ONLY with valid JSON, no markdown or backticks. Include ALL fields in the response.`\n        }]\n      }\n\n      console.log('🚀 Making request to OpenRouter API...')\n      const response = await fetch(\"https://openrouter.ai/api/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Authorization\": `Bearer ${API_KEY}`,\n          \"HTTP-Referer\": \"https://github.com/amazon-market-analysis\",\n          \"X-Title\": \"Amazon Market Analysis\",\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(requestBody)\n      })\n\n      if (!response.ok) {\n        throw new Error(`OpenRouter API request failed: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n      const content = data.choices[0]?.message?.content\n\n      if (!content) {\n        throw new Error('No content received from OpenRouter AI service')\n      }\n\n      console.log('✅ Received response from OpenRouter AI')\n\n      // Clean the response and parse JSON\n      const cleanedContent = content.replace(/```json|```/g, '').trim()\n      return JSON.parse(cleanedContent)\n    } catch (error) {\n      console.error('❌ Error generating market analysis:', error)\n      throw new Error('Failed to generate market analysis')\n    }\n  }\n\n  static async generateProductInsights(\n    productDetails: any,\n    reviews: string[]\n  ): Promise<{\n    strengths: string[]\n    weaknesses: string[]\n    opportunities: string[]\n    threats: string[]\n    recommendations: string[]\n  }> {\n    try {\n      const requestBody = {\n        model: \"deepseek/deepseek-r1:free\",\n        messages: [{\n          role: \"user\",\n          content: `Analyze this product and its reviews to provide strategic insights:\n\n                   Product: ${productDetails.product_title}\n                   Price: ${productDetails.product_price}\n                   Rating: ${productDetails.product_star_rating}\n                   Reviews Count: ${productDetails.product_num_ratings}\n\n                   Sample Reviews: ${reviews.slice(0, 50).join('\\n')}\n\n                   Provide a SWOT analysis and recommendations in JSON format:\n                   {\n                     \"strengths\": [\"strength1\", \"strength2\", \"strength3\"],\n                     \"weaknesses\": [\"weakness1\", \"weakness2\", \"weakness3\"],\n                     \"opportunities\": [\"opportunity1\", \"opportunity2\", \"opportunity3\"],\n                     \"threats\": [\"threat1\", \"threat2\", \"threat3\"],\n                     \"recommendations\": [\"rec1\", \"rec2\", \"rec3\"]\n                   }\n\n                   IMPORTANT: Respond ONLY with valid JSON, no markdown or backticks.`\n        }]\n      }\n\n      const response = await fetch(\"https://openrouter.ai/api/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Authorization\": `Bearer ${API_KEY}`,\n          \"HTTP-Referer\": \"https://github.com/amazon-product-insights\",\n          \"X-Title\": \"Amazon Product Insights\",\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(requestBody)\n      })\n\n      if (!response.ok) {\n        throw new Error(`API request failed: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n      const content = data.choices[0]?.message?.content\n\n      if (!content) {\n        throw new Error('No content received from AI service')\n      }\n\n      // Clean the response and parse JSON\n      const cleanedContent = content.replace(/```json|```/g, '').trim()\n      return JSON.parse(cleanedContent)\n    } catch (error) {\n      console.error('Error generating product insights:', error)\n      throw new Error('Failed to generate product insights')\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,UAAU,QAAQ,GAAG,CAAC,kBAAkB;AA2EvC,MAAM;IACX,aAAa,eACX,OAAiB,EACjB,SAAsB,IAAI,EACK;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAS;gBAAO;YACzC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,uBACX,QAAe,EACf,KAAa,EACkB;QAC/B,IAAI;YACF,QAAQ,GAAG,CAAC,+DAA+D;YAC3E,QAAQ,GAAG,CAAC,2BAA2B,SAAS,MAAM;YAEtD,sCAAsC;YACtC,IAAI,CAAC,WAAW,YAAY,gCAAgC;gBAC1D,QAAQ,GAAG,CAAC;gBACZ,MAAM,mBAAmB,SAAS,MAAM,GAAG,IAAI,SAAS,SAAS,MAAM,GAAG,IAAI,WAAW;gBACzF,MAAM,aAAa;oBAAC;oBAAc;oBAAc;iBAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;gBAExF,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,WAAW,EAAE,aAAa,EAAE,QAAQ,KAAK,OAAO,MAAM,KAAK,SAAS,MAAM;gBACxI,MAAM,WAAW,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,WAAW,EAAE,aAAa,EAAE,QAAQ,KAAK,OAAO;gBAC/F,MAAM,WAAW,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,WAAW,EAAE,aAAa,EAAE,QAAQ,KAAK,OAAO;gBAE/F,MAAM,eAAqC;oBACzC,aAAa,CAAC,kBAAkB,EAAE,SAAS,MAAM,GAAG,IAAI,gBAAgB,EAAE,KAAK,KAAK,CAAC,WAAW,SAAS,MAAM,GAAG,MAAM,iBAAiB,CAAC;oBAC1I,mBAAmB;oBACnB,mBAAmB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBACpD,cAAc;wBACZ,CAAC,IAAI,EAAE,MAAM,yDAAyD,EAAE,SAAS,MAAM,CAAC,kBAAkB,CAAC;wBAC3G,CAAC,yBAAyB,EAAE,SAAS,KAAK,EAAE,SAAS,4BAA4B,CAAC;wBAClF,CAAC,4EAA4E,CAAC;wBAC9E,CAAC,aAAa,EAAE,WAAW,yBAAyB,EAAE,eAAe,eAAe,mBAAmB,eAAe,eAAe,sBAAsB,oBAAoB;wBAC/K,CAAC,oCAAoC,EAAE,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,EAAE,MAAM,CAAC,QAAQ,EAAE,SAAS,MAAM,CAAC,mCAAmC,CAAC;qBACpJ;oBACD,qBAAqB;wBACnB,CAAC,YAAY,EAAE,KAAK,KAAK,CAAC,WAAW,KAAK,EAAE,EAAE,KAAK,KAAK,CAAC,WAAW,KAAK,qCAAqC,CAAC;wBAC/G,CAAC,wEAAwE,CAAC;wBAC1E,CAAC,uEAAuE,CAAC;wBACzE,CAAC,sDAAsD,EAAE,MAAM,SAAS,CAAC;wBACzE,CAAC,+DAA+D,CAAC;qBAClE;oBACD,gBAAgB;wBACd,eAAe;wBACf,aAAa;4BAAE,KAAK;4BAAU,KAAK;wBAAS;wBAC5C,aAAa;wBACb,gBAAgB;4BACd,SAAS,KAAK,KAAK,CAAC,WAAW;4BAC/B,UAAU,KAAK,KAAK,CAAC,WAAW;4BAChC,SAAS,KAAK,KAAK,CAAC,WAAW;wBACjC;oBACF;oBACA,qBAAqB;wBACnB,YAAY;4BAAC;4BAAS;4BAAW;4BAAQ;4BAAO;yBAAQ,CAAC,KAAK,CAAC,GAAG;wBAClE,gBAAgB;4BACd;gCACE,OAAO;gCACP,cAAc;gCACd,gBAAgB;oCAAC;oCAAqB;oCAAwB;iCAAkB;4BAClF;4BACA;gCACE,OAAO;gCACP,cAAc;gCACd,gBAAgB;oCAAC;oCAAuB;oCAAc;iCAAmB;4BAC3E;yBACD;wBACD,kBAAkB;4BAChB;4BACA;4BACA;yBACD;oBACH;oBACA,mBAAmB;wBACjB,qBAAqB;4BAAC;4BAAoB;4BAAiB;yBAA6B;wBACxF,iBAAiB;4BAAC;4BAA6B;4BAA6B;yBAA8B;wBAC1G,aAAa;4BAAC;4BAAiB;4BAAuB;yBAA4B;wBAClF,oBAAoB;oBACtB;oBACA,eAAe;wBACb,mBAAmB;wBACnB,mBAAmB;wBACnB,mBAAmB;4BAAC;4BAAyB;4BAAkB;yBAAuB;wBACtF,iBAAiB;oBACnB;oBACA,iBAAiB;wBACf,cAAc;4BAAC;4BAA4B;4BAA4B;yBAA0B;wBACjG,qBAAqB;4BAAC;4BAAuB;4BAAc;yBAAkB;wBAC7E,mBAAmB;4BAAC;4BAAqB;4BAAiC;yBAAqB;wBAC/F,YAAY;oBACd;oBACA,gBAAgB;wBACd,yBAAyB;4BACvB,KAAK,KAAK,KAAK,CAAC,WAAW;4BAC3B,KAAK,KAAK,KAAK,CAAC,WAAW;4BAC3B,SAAS;wBACX;wBACA,+BAA+B;4BAC7B;4BACA;4BACA;4BACA;yBACD;wBACD,oBAAoB;4BAAC;4BAAc;4BAA4B;4BAA2B;yBAAoB;wBAC9G,oBAAoB;oBACtB;oBACA,cAAc,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;4BAC3D,MAAM,QAAQ,IAAI;4BAClB,OAAO,QAAQ,aAAa;4BAC5B,OAAO,QAAQ,aAAa,IAAI;4BAChC,QAAQ,QAAQ,mBAAmB;4BACnC,SAAS,QAAQ,mBAAmB;4BACpC,cAAc,QAAQ,YAAY,IAAI;4BACtC,gBAAgB,QAAQ,cAAc,IAAI;4BAC1C,uBAAuB,QAAQ,IAAI,oCAAoC,QAAQ,KAAK,sCAAsC,QAAQ,KAAK,qCAAqC;4BAC5K,iBAAkB,QAAQ,IAAI,WAAW,QAAQ,IAAI,eAAe,QAAQ,KAAK,aAAa;wBAChG,CAAC;gBACH;gBACA,OAAO;YACT;YAEA,uCAAuC;YACvC,MAAM,iBAAiB,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,IAAK,CAAC;oBACrD,OAAO,EAAE,aAAa;oBACtB,OAAO,EAAE,aAAa;oBACtB,QAAQ,EAAE,mBAAmB;oBAC7B,SAAS,EAAE,mBAAmB;oBAC9B,gBAAgB,EAAE,cAAc;oBAChC,cAAc,EAAE,YAAY;gBAC9B,CAAC;YAED,MAAM,cAAc;gBAClB,OAAO;gBACP,UAAU;oBAAC;wBACT,MAAM;wBACN,SAAS,CAAC,YAAY,EAAE,MAAM;;;AAGxC,EAAE,KAAK,SAAS,CAAC,gBAAgB,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sGAyE4D,CAAC;oBAC/F;iBAAE;YACJ;YAEA,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,MAAM,iDAAiD;gBAC5E,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,SAAS;oBACpC,gBAAgB;oBAChB,WAAW;oBACX,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,UAAU,EAAE;YACzE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,UAAU,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS;YAE1C,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC;YAEZ,oCAAoC;YACpC,MAAM,iBAAiB,QAAQ,OAAO,CAAC,gBAAgB,IAAI,IAAI;YAC/D,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,wBACX,cAAmB,EACnB,OAAiB,EAOhB;QACD,IAAI;YACF,MAAM,cAAc;gBAClB,OAAO;gBACP,UAAU;oBAAC;wBACT,MAAM;wBACN,SAAS,CAAC;;4BAEQ,EAAE,eAAe,aAAa,CAAC;0BACjC,EAAE,eAAe,aAAa,CAAC;2BAC9B,EAAE,eAAe,mBAAmB,CAAC;kCAC9B,EAAE,eAAe,mBAAmB,CAAC;;mCAEpC,EAAE,QAAQ,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM;;;;;;;;;;;qFAWgB,CAAC;oBAC9E;iBAAE;YACJ;YAEA,MAAM,WAAW,MAAM,MAAM,iDAAiD;gBAC5E,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,SAAS;oBACpC,gBAAgB;oBAChB,WAAW;oBACX,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,UAAU,EAAE;YAC9D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,UAAU,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS;YAE1C,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,MAAM,iBAAiB,QAAQ,OAAO,CAAC,gBAAgB,IAAI,IAAI;YAC/D,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/app/dashboard/product/%5Basin%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useParams } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { \n  Star,\n  ExternalLink,\n  TrendingUp,\n  MessageSquare,\n  BarChart3,\n  Package,\n  DollarSign,\n  Users,\n  ThumbsUp,\n  ThumbsDown,\n  Loader2\n} from 'lucide-react'\nimport { AmazonAPIService, type ProductDetails, type AmazonReview } from '@/lib/services/amazon-api'\nimport { OpenRouterAIService, type ReviewAnalysisResult } from '@/lib/services/openrouter-ai'\nimport { formatPrice, formatNumber, getStarRating } from '@/lib/utils'\n\nexport default function ProductAnalysis() {\n  const params = useParams()\n  const asin = params.asin as string\n\n  const [product, setProduct] = useState<ProductDetails | null>(null)\n  const [reviews, setReviews] = useState<AmazonReview[]>([])\n  const [reviewAnalysis, setReviewAnalysis] = useState<ReviewAnalysisResult | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [analyzingReviews, setAnalyzingReviews] = useState(false)\n\n  useEffect(() => {\n    if (asin) {\n      loadProductData()\n    }\n  }, [asin])\n\n  const loadProductData = async () => {\n    try {\n      setLoading(true)\n      \n      // Load product details and reviews in parallel\n      const [productData, reviewsData] = await Promise.all([\n        AmazonAPIService.getProductDetails(asin),\n        AmazonAPIService.getProductReviews(asin, 1)\n      ])\n\n      setProduct(productData)\n      setReviews(reviewsData.data)\n    } catch (error) {\n      console.error('Failed to load product data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const analyzeReviews = async () => {\n    if (!reviews.length) return\n\n    try {\n      setAnalyzingReviews(true)\n      const reviewTexts = reviews.map(review => review.review_comment).filter(Boolean)\n      const analysis = await OpenRouterAIService.analyzeReviews(reviewTexts)\n      setReviewAnalysis(analysis)\n    } catch (error) {\n      console.error('Failed to analyze reviews:', error)\n    } finally {\n      setAnalyzingReviews(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px]\">\n        <div className=\"text-center\">\n          <Loader2 className=\"w-8 h-8 animate-spin mx-auto mb-4\" />\n          <p className=\"text-gray-600\">Loading product analysis...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!product) {\n    return (\n      <div className=\"text-center py-12\">\n        <Package className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Product not found</h3>\n        <p className=\"text-gray-600\">The product with ASIN {asin} could not be found.</p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Product Analysis</h1>\n        <p className=\"text-gray-600 mt-2\">\n          Comprehensive analysis for ASIN: {asin}\n        </p>\n      </div>\n\n      {/* Product Overview */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex gap-6\">\n            <div className=\"flex-shrink-0\">\n              <img\n                src={product.product_photos[0]}\n                alt={product.product_title}\n                className=\"w-48 h-48 object-cover rounded-lg border\"\n              />\n            </div>\n            <div className=\"flex-1 space-y-4\">\n              <div>\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n                  {product.product_title}\n                </h2>\n                <p className=\"text-gray-600\">ASIN: {asin}</p>\n              </div>\n\n              <div className=\"flex items-center gap-6\">\n                <div className=\"flex items-center\">\n                  <span className=\"text-yellow-400 mr-2 text-lg\">\n                    {getStarRating(parseFloat(product.product_star_rating))}\n                  </span>\n                  <span className=\"text-lg font-medium\">\n                    {product.product_star_rating}\n                  </span>\n                </div>\n                <span className=\"text-gray-600\">\n                  {formatNumber(product.product_num_ratings)} reviews\n                </span>\n              </div>\n\n              <div className=\"flex items-center gap-4\">\n                <div className=\"text-3xl font-bold text-gray-900\">\n                  {product.product_price}\n                </div>\n                {product.product_original_price && (\n                  <div className=\"text-xl text-gray-500 line-through\">\n                    {product.product_original_price}\n                  </div>\n                )}\n              </div>\n\n              <div className=\"flex gap-2\">\n                <Button asChild>\n                  <a \n                    href={product.product_url} \n                    target=\"_blank\" \n                    rel=\"noopener noreferrer\"\n                  >\n                    <ExternalLink className=\"w-4 h-4 mr-2\" />\n                    View on Amazon\n                  </a>\n                </Button>\n                <Button variant=\"outline\">\n                  <BarChart3 className=\"w-4 h-4 mr-2\" />\n                  Market Analysis\n                </Button>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Rating</CardTitle>\n            <Star className=\"w-4 h-4 text-yellow-400\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{product.product_star_rating}</div>\n            <p className=\"text-xs text-gray-600\">out of 5 stars</p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Reviews</CardTitle>\n            <MessageSquare className=\"w-4 h-4 text-blue-400\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{formatNumber(product.product_num_ratings)}</div>\n            <p className=\"text-xs text-gray-600\">customer reviews</p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Price</CardTitle>\n            <DollarSign className=\"w-4 h-4 text-green-400\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{product.product_price}</div>\n            <p className=\"text-xs text-gray-600\">current price</p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Category</CardTitle>\n            <Package className=\"w-4 h-4 text-purple-400\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-sm font-bold\">{product.category_tree[0]?.name || 'N/A'}</div>\n            <p className=\"text-xs text-gray-600\">main category</p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Review Analysis */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <CardTitle>Review Analysis</CardTitle>\n              <CardDescription>\n                AI-powered analysis of customer reviews to identify key insights\n              </CardDescription>\n            </div>\n            <Button \n              onClick={analyzeReviews} \n              disabled={analyzingReviews || !reviews.length}\n            >\n              {analyzingReviews ? (\n                <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n              ) : (\n                <TrendingUp className=\"w-4 h-4 mr-2\" />\n              )}\n              {analyzingReviews ? 'Analyzing...' : 'Analyze Reviews'}\n            </Button>\n          </div>\n        </CardHeader>\n        <CardContent>\n          {reviewAnalysis ? (\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              {/* Pros */}\n              <div>\n                <h4 className=\"font-semibold text-green-700 mb-3 flex items-center\">\n                  <ThumbsUp className=\"w-4 h-4 mr-2\" />\n                  Top Positive Points\n                </h4>\n                <div className=\"space-y-2\">\n                  {reviewAnalysis.pros.map((pro, index) => (\n                    <div key={index} className=\"flex items-center justify-between p-3 bg-green-50 rounded-lg\">\n                      <span className=\"text-sm text-green-800\">{pro.text}</span>\n                      <span className=\"text-xs bg-green-200 text-green-800 px-2 py-1 rounded-full\">\n                        {pro.count} mentions\n                      </span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Cons */}\n              <div>\n                <h4 className=\"font-semibold text-red-700 mb-3 flex items-center\">\n                  <ThumbsDown className=\"w-4 h-4 mr-2\" />\n                  Top Negative Points\n                </h4>\n                <div className=\"space-y-2\">\n                  {reviewAnalysis.cons.map((con, index) => (\n                    <div key={index} className=\"flex items-center justify-between p-3 bg-red-50 rounded-lg\">\n                      <span className=\"text-sm text-red-800\">{con.text}</span>\n                      <span className=\"text-xs bg-red-200 text-red-800 px-2 py-1 rounded-full\">\n                        {con.count} mentions\n                      </span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <MessageSquare className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600\">\n                Click \"Analyze Reviews\" to get AI-powered insights from customer feedback\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Product Description */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Product Description</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <p className=\"text-gray-700 leading-relaxed\">\n            {product.product_description || 'No description available.'}\n          </p>\n        </CardContent>\n      </Card>\n\n      {/* Recent Reviews */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Reviews ({reviews.length})</CardTitle>\n          <CardDescription>\n            Latest customer feedback and ratings\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {reviews.slice(0, 5).map((review, index) => (\n              <div key={index} className=\"border-b pb-4 last:border-b-0\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <span className=\"font-medium text-gray-900\">{review.review_author}</span>\n                    {review.is_verified_purchase && (\n                      <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">\n                        Verified Purchase\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className=\"text-yellow-400 mr-1\">\n                      {getStarRating(review.review_star_rating)}\n                    </span>\n                    <span className=\"text-sm text-gray-600\">{review.review_date}</span>\n                  </div>\n                </div>\n                <h5 className=\"font-medium text-gray-900 mb-1\">{review.review_title}</h5>\n                <p className=\"text-gray-700 text-sm\">{review.review_comment}</p>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AArBA;;;;;;;;;;AAuBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,OAAO,OAAO,IAAI;IAExB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAClF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YAEX,+CAA+C;YAC/C,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnD,uIAAA,CAAA,mBAAgB,CAAC,iBAAiB,CAAC;gBACnC,uIAAA,CAAA,mBAAgB,CAAC,iBAAiB,CAAC,MAAM;aAC1C;YAED,WAAW;YACX,WAAW,YAAY,IAAI;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ,MAAM,EAAE;QAErB,IAAI;YACF,oBAAoB;YACpB,MAAM,cAAc,QAAQ,GAAG,CAAC,CAAA,SAAU,OAAO,cAAc,EAAE,MAAM,CAAC;YACxE,MAAM,WAAW,MAAM,0IAAA,CAAA,sBAAmB,CAAC,cAAc,CAAC;YAC1D,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAE,WAAU;;wBAAgB;wBAAuB;wBAAK;;;;;;;;;;;;;IAG/D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;;4BAAqB;4BACE;;;;;;;;;;;;;0BAKtC,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAK,QAAQ,cAAc,CAAC,EAAE;oCAC9B,KAAK,QAAQ,aAAa;oCAC1B,WAAU;;;;;;;;;;;0CAGd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,QAAQ,aAAa;;;;;;0DAExB,8OAAC;gDAAE,WAAU;;oDAAgB;oDAAO;;;;;;;;;;;;;kDAGtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,WAAW,QAAQ,mBAAmB;;;;;;kEAEvD,8OAAC;wDAAK,WAAU;kEACb,QAAQ,mBAAmB;;;;;;;;;;;;0DAGhC,8OAAC;gDAAK,WAAU;;oDACb,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,mBAAmB;oDAAE;;;;;;;;;;;;;kDAI/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,aAAa;;;;;;4CAEvB,QAAQ,sBAAsB,kBAC7B,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,sBAAsB;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;0DACb,cAAA,8OAAC;oDACC,MAAM,QAAQ,WAAW;oDACzB,QAAO;oDACP,KAAI;;sEAEJ,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAI7C,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;;kEACd,8OAAC,kNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;0CAElB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAsB,QAAQ,mBAAmB;;;;;;kDAChE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAIzC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;0CAE3B,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAsB,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,mBAAmB;;;;;;kDAC7E,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAIzC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAsB,QAAQ,aAAa;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAIzC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;0CAErB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAqB,QAAQ,aAAa,CAAC,EAAE,EAAE,QAAQ;;;;;;kDACtE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,oBAAoB,CAAC,QAAQ,MAAM;;wCAE5C,iCACC,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAEvB,mBAAmB,iBAAiB;;;;;;;;;;;;;;;;;;kCAI3C,8OAAC,gIAAA,CAAA,cAAW;kCACT,+BACC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC;4CAAI,WAAU;sDACZ,eAAe,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC7B,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAK,WAAU;sEAA0B,IAAI,IAAI;;;;;;sEAClD,8OAAC;4DAAK,WAAU;;gEACb,IAAI,KAAK;gEAAC;;;;;;;;mDAHL;;;;;;;;;;;;;;;;8CAWhB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGzC,8OAAC;4CAAI,WAAU;sDACZ,eAAe,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC7B,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAK,WAAU;sEAAwB,IAAI,IAAI;;;;;;sEAChD,8OAAC;4DAAK,WAAU;;gEACb,IAAI,KAAK;gEAAC;;;;;;;;mDAHL;;;;;;;;;;;;;;;;;;;;;iDAWlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BASrC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAE,WAAU;sCACV,QAAQ,mBAAmB,IAAI;;;;;;;;;;;;;;;;;0BAMtC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;;oCAAC;oCAAiB,QAAQ,MAAM;oCAAC;;;;;;;0CAC3C,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBAChC,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA6B,OAAO,aAAa;;;;;;wDAChE,OAAO,oBAAoB,kBAC1B,8OAAC;4DAAK,WAAU;sEAA6D;;;;;;;;;;;;8DAKjF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,kBAAkB;;;;;;sEAE1C,8OAAC;4DAAK,WAAU;sEAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;sDAG/D,8OAAC;4CAAG,WAAU;sDAAkC,OAAO,YAAY;;;;;;sDACnE,8OAAC;4CAAE,WAAU;sDAAyB,OAAO,cAAc;;;;;;;mCAlBnD;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BxB", "debugId": null}}]}