{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/node_modules/next/src/client/components/unauthorized-error.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from './http-access-fallback/error-fallback'\n\nexport default function Unauthorized() {\n  return (\n    <HTTPAccessErrorFallback\n      status={401}\n      message=\"You're not authorized to access this page.\"\n    />\n  )\n}\n"], "names": ["Unauthorized", "HTTPAccessErrorFallback", "status", "message"], "mappings": ";;;;+BAEA,WAAA;;;eAAwBA;;;;+BAFgB;AAEzB,SAASA;IACtB,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACC,eAAAA,uBAAuB,EAAA;QACtBC,QAAQ;QACRC,SAAQ;;AAGd", "ignoreList": [0], "debugId": null}}]}