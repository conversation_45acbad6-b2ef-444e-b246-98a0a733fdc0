{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatNumber(num: number) {\n  return new Intl.NumberFormat('en-US').format(num)\n}\n\nexport function truncateText(text: string, maxLength: number) {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\nexport function getStarRating(rating: number) {\n  const stars = []\n  const fullStars = Math.floor(rating)\n  const hasHalfStar = rating % 1 !== 0\n\n  for (let i = 0; i < fullStars; i++) {\n    stars.push('★')\n  }\n\n  if (hasHalfStar) {\n    stars.push('☆')\n  }\n\n  while (stars.length < 5) {\n    stars.push('☆')\n  }\n\n  return stars.join('')\n}\n\nexport function extractASIN(url: string): string | null {\n  const asinRegex = /\\/([A-Z0-9]{10})(?:[/?]|$)/\n  const match = url.match(asinRegex)\n  return match ? match[1] : null\n}\n\nexport function isValidASIN(asin: string): boolean {\n  return /^[A-Z0-9]{10}$/.test(asin)\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,cAAc,MAAc;IAC1C,MAAM,QAAQ,EAAE;IAChB,MAAM,YAAY,KAAK,KAAK,CAAC;IAC7B,MAAM,cAAc,SAAS,MAAM;IAEnC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;QAClC,MAAM,IAAI,CAAC;IACb;IAEA,IAAI,aAAa;QACf,MAAM,IAAI,CAAC;IACb;IAEA,MAAO,MAAM,MAAM,GAAG,EAAG;QACvB,MAAM,IAAI,CAAC;IACb;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB;AAEO,SAAS,YAAY,GAAW;IACrC,MAAM,YAAY;IAClB,MAAM,QAAQ,IAAI,KAAK,CAAC;IACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC5B;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,iBAAiB,IAAI,CAAC;AAC/B", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/components/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { \n  Search, \n  BarChart3, \n  Package, \n  Settings, \n  User,\n  Menu,\n  X\n} from 'lucide-react'\nimport { useState } from 'react'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: BarChart3 },\n  { name: 'Product Search', href: '/dashboard/search', icon: Search },\n  { name: 'Market Analysis', href: '/dashboard/market-analysis', icon: Package },\n  { name: 'Settings', href: '/dashboard/settings', icon: Settings },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n                NextSelling\n              </Link>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n              {navigation.map((item) => {\n                const Icon = item.icon\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${\n                      pathname === item.href\n                        ? 'border-blue-500 text-gray-900'\n                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'\n                    }`}\n                  >\n                    <Icon className=\"w-4 h-4 mr-2\" />\n                    {item.name}\n                  </Link>\n                )\n              })}\n            </div>\n          </div>\n          <div className=\"hidden sm:ml-6 sm:flex sm:items-center\">\n            <Button variant=\"outline\" size=\"sm\" className=\"mr-2\">\n              <User className=\"w-4 h-4 mr-2\" />\n              Account\n            </Button>\n            <Button size=\"sm\">\n              Upgrade to Pro\n            </Button>\n          </div>\n          <div className=\"-mr-2 flex items-center sm:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              {mobileMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {mobileMenuOpen && (\n        <div className=\"sm:hidden\">\n          <div className=\"pt-2 pb-3 space-y-1\">\n            {navigation.map((item) => {\n              const Icon = item.icon\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`block pl-3 pr-4 py-2 border-l-4 text-base font-medium ${\n                    pathname === item.href\n                      ? 'bg-blue-50 border-blue-500 text-blue-700'\n                      : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'\n                  }`}\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <div className=\"flex items-center\">\n                    <Icon className=\"w-4 h-4 mr-3\" />\n                    {item.name}\n                  </div>\n                </Link>\n              )\n            })}\n          </div>\n          <div className=\"pt-4 pb-3 border-t border-gray-200\">\n            <div className=\"flex items-center px-4 space-y-2 flex-col\">\n              <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                <User className=\"w-4 h-4 mr-2\" />\n                Account\n              </Button>\n              <Button size=\"sm\" className=\"w-full\">\n                Upgrade to Pro\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAdA;;;;;;;AAgBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,YAAS;IAAC;IACzD;QAAE,MAAM;QAAkB,MAAM;QAAqB,MAAM,sMAAA,CAAA,SAAM;IAAC;IAClE;QAAE,MAAM;QAAmB,MAAM;QAA8B,MAAM,wMAAA,CAAA,UAAO;IAAC;IAC7E;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACjE;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAmC;;;;;;;;;;;8CAI9D,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,OAAO,KAAK,IAAI;wCACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,kEAAkE,EAC5E,aAAa,KAAK,IAAI,GAClB,kCACA,8EACJ;;8DAEF,8OAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;2CATL,KAAK,IAAI;;;;;oCAYpB;;;;;;;;;;;;sCAGJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;sDAC5C,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;8CAAK;;;;;;;;;;;;sCAIpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,kBAAkB,CAAC;0CAEjC,+BACC,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzB,gCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,OAAO,KAAK,IAAI;4BACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sDAAsD,EAChE,aAAa,KAAK,IAAI,GAClB,6CACA,+FACJ;gCACF,SAAS,IAAM,kBAAkB;0CAEjC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;wCACf,KAAK,IAAI;;;;;;;+BAXP,KAAK,IAAI;;;;;wBAepB;;;;;;kCAEF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;sDAC5C,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}]}