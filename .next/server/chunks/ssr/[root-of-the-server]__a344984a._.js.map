{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mUACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/lib/services/openrouter-ai.ts"], "sourcesContent": ["const API_KEY = process.env.OPENROUTER_API_KEY!\n\nexport interface ReviewAnalysisResult {\n  pros: Array<{ text: string; count: number }>\n  cons: Array<{ text: string; count: number }>\n  sentiment_score?: number\n  summary?: string\n}\n\nexport interface MarketAnalysisResult {\n  market_size: string\n  competition_level: 'low' | 'medium' | 'high'\n  opportunity_score: number\n  key_insights: string[]\n  recommended_actions: string[]\n  price_analysis: {\n    average_price: number\n    price_range: { min: number; max: number }\n    price_trend: 'increasing' | 'decreasing' | 'stable'\n    profit_margins: {\n      low_end: number\n      high_end: number\n      average: number\n    }\n  }\n  competitor_analysis: {\n    top_brands: string[]\n    market_leaders: {\n      brand: string\n      market_share: number\n      key_advantages: string[]\n    }[]\n    competitive_gaps: string[]\n  }\n  customer_analysis: {\n    target_demographics: string[]\n    buying_patterns: string[]\n    pain_points: string[]\n    satisfaction_level: number\n  }\n  market_trends: {\n    seasonal_patterns: string\n    growth_trajectory: 'growing' | 'stable' | 'declining'\n    emerging_features: string[]\n    market_maturity: 'emerging' | 'growing' | 'mature' | 'declining'\n  }\n  risk_assessment: {\n    market_risks: string[]\n    competitive_threats: string[]\n    opportunity_risks: string[]\n    risk_level: 'low' | 'medium' | 'high'\n  }\n  entry_strategy: {\n    recommended_price_point: {\n      min: number\n      max: number\n      optimal: number\n    }\n    differentiation_opportunities: string[]\n    marketing_channels: string[]\n    timeline_to_market: string\n  }\n  top_products: Array<{\n    asin: string\n    title: string\n    price: string\n    rating: string\n    reviews: number\n    sales_volume: string\n    is_best_seller: boolean\n    competitive_advantage: string\n    market_position: 'leader' | 'challenger' | 'follower' | 'niche'\n  }>\n}\n\nexport class OpenRouterAIService {\n  static async analyzeReviews(\n    reviews: string[],\n    locale: 'en' | 'fr' = 'en'\n  ): Promise<ReviewAnalysisResult> {\n    try {\n      const response = await fetch('/api/ai/analyze-reviews', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ reviews, locale })\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to analyze reviews')\n      }\n\n      return await response.json()\n    } catch (error) {\n      console.error('Error analyzing reviews:', error)\n      throw new Error('Failed to analyze reviews')\n    }\n  }\n\n  static async generateMarketAnalysis(\n    products: any[],\n    niche: string\n  ): Promise<MarketAnalysisResult> {\n    try {\n      console.log('🤖 Generating market analysis with OpenRouter AI for niche:', niche)\n      console.log('📊 Products to analyze:', products.length)\n\n      // If no API key, return mock analysis\n      if (!API_KEY || API_KEY === 'your_openrouter_api_key_here') {\n        console.log('⚠️ No valid OpenRouter API key found, returning mock analysis')\n        const competitionLevel = products.length > 5 ? 'high' : products.length > 2 ? 'medium' : 'low'\n        const priceTrend = ['increasing', 'decreasing', 'stable'][Math.floor(Math.random() * 3)] as 'increasing' | 'decreasing' | 'stable'\n\n        const avgPrice = Math.floor(products.reduce((sum, p) => sum + parseFloat(p.product_price?.replace('$', '') || '0'), 0) / products.length)\n        const minPrice = Math.min(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))\n        const maxPrice = Math.max(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))\n\n        const mockAnalysis: MarketAnalysisResult = {\n          market_size: `Large market with ${products.length * 100}+ products and $${Math.floor(avgPrice * products.length * 1000)}M+ annual revenue`,\n          competition_level: competitionLevel as 'low' | 'medium' | 'high',\n          opportunity_score: Math.floor(Math.random() * 40) + 60, // 60-100\n          key_insights: [\n            `The ${niche} market shows strong demand with consistent sales across ${products.length} analyzed products`,\n            `Price points range from $${minPrice} to $${maxPrice} with healthy profit margins`,\n            `Customer satisfaction is generally high with average ratings above 4.0 stars`,\n            `Market shows ${priceTrend} price trends indicating ${priceTrend === 'increasing' ? 'growing demand' : priceTrend === 'decreasing' ? 'price competition' : 'market stability'}`,\n            `Best-selling products dominate with ${products.filter(p => p.is_best_seller).length} out of ${products.length} products having best-seller status`\n          ],\n          recommended_actions: [\n            `Target the $${Math.floor(avgPrice * 0.8)}-$${Math.floor(avgPrice * 1.2)} price range for optimal market entry`,\n            `Focus on quality and customer service to compete with established brands`,\n            `Leverage emerging features and customer pain points for differentiation`,\n            `Consider seasonal trends and inventory management for ${niche} products`,\n            `Build strong brand presence through targeted marketing channels`\n          ],\n          price_analysis: {\n            average_price: avgPrice,\n            price_range: { min: minPrice, max: maxPrice },\n            price_trend: priceTrend,\n            profit_margins: {\n              low_end: Math.floor(minPrice * 0.3),\n              high_end: Math.floor(maxPrice * 0.6),\n              average: Math.floor(avgPrice * 0.45)\n            }\n          },\n          competitor_analysis: {\n            top_brands: ['Apple', 'Samsung', 'Sony', 'JBL', 'Anker'].slice(0, 3),\n            market_leaders: [\n              {\n                brand: 'Market Leader',\n                market_share: 35,\n                key_advantages: ['Brand recognition', 'Distribution network', 'Product quality']\n              },\n              {\n                brand: 'Strong Challenger',\n                market_share: 25,\n                key_advantages: ['Competitive pricing', 'Innovation', 'Customer service']\n              }\n            ],\n            competitive_gaps: [\n              'Limited options in mid-range pricing',\n              'Lack of eco-friendly alternatives',\n              'Poor customer support in some segments'\n            ]\n          },\n          customer_analysis: {\n            target_demographics: ['Tech enthusiasts', 'Professionals', 'Budget-conscious consumers'],\n            buying_patterns: ['Research-driven purchases', 'Price comparison shopping', 'Review-influenced decisions'],\n            pain_points: ['Complex setup', 'Durability concerns', 'Limited warranty coverage'],\n            satisfaction_level: 4.2\n          },\n          market_trends: {\n            seasonal_patterns: 'Peak sales during Q4 holidays and back-to-school season',\n            growth_trajectory: 'growing' as const,\n            emerging_features: ['Wireless connectivity', 'AI integration', 'Sustainability focus'],\n            market_maturity: 'growing' as const\n          },\n          risk_assessment: {\n            market_risks: ['Economic downturn impact', 'Supply chain disruptions', 'Technology obsolescence'],\n            competitive_threats: ['New market entrants', 'Price wars', 'Patent disputes'],\n            opportunity_risks: ['Market saturation', 'Changing consumer preferences', 'Regulatory changes'],\n            risk_level: competitionLevel as 'low' | 'medium' | 'high'\n          },\n          entry_strategy: {\n            recommended_price_point: {\n              min: Math.floor(avgPrice * 0.8),\n              max: Math.floor(avgPrice * 1.2),\n              optimal: avgPrice\n            },\n            differentiation_opportunities: [\n              'Superior customer support',\n              'Innovative features',\n              'Competitive pricing',\n              'Better user experience'\n            ],\n            marketing_channels: ['Amazon PPC', 'Social media advertising', 'Influencer partnerships', 'Content marketing'],\n            timeline_to_market: '3-6 months for product development and launch'\n          },\n          top_products: products.slice(0, 20).map((product, index) => ({\n            asin: product.asin,\n            title: product.product_title,\n            price: product.product_price || 'N/A',\n            rating: product.product_star_rating,\n            reviews: product.product_num_ratings,\n            sales_volume: product.sales_volume || 'N/A',\n            is_best_seller: product.is_best_seller || false,\n            competitive_advantage: index < 5 ? 'Market leader with strong brand' : index < 10 ? 'Strong challenger with good value' : index < 15 ? 'Solid follower with niche appeal' : 'Emerging player with potential',\n            market_position: (index < 3 ? 'leader' : index < 8 ? 'challenger' : index < 15 ? 'follower' : 'niche') as 'leader' | 'challenger' | 'follower' | 'niche'\n          }))\n        }\n        return mockAnalysis\n      }\n\n      // Prepare product data for AI analysis\n      const productSummary = products.slice(0, 10).map(p => ({\n        title: p.product_title,\n        price: p.product_price,\n        rating: p.product_star_rating,\n        reviews: p.product_num_ratings,\n        is_best_seller: p.is_best_seller,\n        sales_volume: p.sales_volume\n      }))\n\n      const requestBody = {\n        model: \"deepseek/deepseek-r1:free\",\n        messages: [{\n          role: \"user\",\n          content: `Analyze the ${niche} market based on these Amazon products and provide comprehensive market insights:\n\nProducts Data:\n${JSON.stringify(productSummary, null, 2)}\n\nProvide a detailed market analysis in JSON format with ALL the following fields:\n{\n  \"market_size\": \"detailed description of market size with revenue estimates\",\n  \"competition_level\": \"low|medium|high\",\n  \"opportunity_score\": number_between_0_and_100,\n  \"key_insights\": [\"insight1\", \"insight2\", \"insight3\", \"insight4\", \"insight5\"],\n  \"recommended_actions\": [\"action1\", \"action2\", \"action3\", \"action4\", \"action5\"],\n  \"price_analysis\": {\n    \"average_price\": number,\n    \"price_range\": {\"min\": number, \"max\": number},\n    \"price_trend\": \"increasing|decreasing|stable\",\n    \"profit_margins\": {\n      \"low_end\": number,\n      \"high_end\": number,\n      \"average\": number\n    }\n  },\n  \"competitor_analysis\": {\n    \"top_brands\": [\"brand1\", \"brand2\", \"brand3\"],\n    \"market_leaders\": [\n      {\n        \"brand\": \"brand_name\",\n        \"market_share\": number,\n        \"key_advantages\": [\"advantage1\", \"advantage2\", \"advantage3\"]\n      }\n    ],\n    \"competitive_gaps\": [\"gap1\", \"gap2\", \"gap3\"]\n  },\n  \"customer_analysis\": {\n    \"target_demographics\": [\"demo1\", \"demo2\", \"demo3\"],\n    \"buying_patterns\": [\"pattern1\", \"pattern2\", \"pattern3\"],\n    \"pain_points\": [\"pain1\", \"pain2\", \"pain3\"],\n    \"satisfaction_level\": number_between_1_and_5\n  },\n  \"market_trends\": {\n    \"seasonal_patterns\": \"description of seasonal trends\",\n    \"growth_trajectory\": \"growing|stable|declining\",\n    \"emerging_features\": [\"feature1\", \"feature2\", \"feature3\"],\n    \"market_maturity\": \"emerging|growing|mature|declining\"\n  },\n  \"risk_assessment\": {\n    \"market_risks\": [\"risk1\", \"risk2\", \"risk3\"],\n    \"competitive_threats\": [\"threat1\", \"threat2\", \"threat3\"],\n    \"opportunity_risks\": [\"risk1\", \"risk2\", \"risk3\"],\n    \"risk_level\": \"low|medium|high\"\n  },\n  \"entry_strategy\": {\n    \"recommended_price_point\": {\n      \"min\": number,\n      \"max\": number,\n      \"optimal\": number\n    },\n    \"differentiation_opportunities\": [\"opp1\", \"opp2\", \"opp3\"],\n    \"marketing_channels\": [\"channel1\", \"channel2\", \"channel3\"],\n    \"timeline_to_market\": \"timeline description\"\n  },\n  \"top_products\": [\n    {\n      \"asin\": \"product_asin\",\n      \"title\": \"product_title\",\n      \"price\": \"product_price\",\n      \"rating\": \"product_rating\",\n      \"reviews\": number_of_reviews,\n      \"sales_volume\": \"sales_volume\",\n      \"is_best_seller\": boolean,\n      \"competitive_advantage\": \"advantage description\",\n      \"market_position\": \"leader|challenger|follower|niche\"\n    }\n  ]\n}\n\nIMPORTANT: Respond ONLY with valid JSON, no markdown or backticks. Include ALL fields in the response.`\n        }]\n      }\n\n      console.log('🚀 Making request to OpenRouter API...')\n      const response = await fetch(\"https://openrouter.ai/api/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Authorization\": `Bearer ${API_KEY}`,\n          \"HTTP-Referer\": \"https://github.com/amazon-market-analysis\",\n          \"X-Title\": \"Amazon Market Analysis\",\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(requestBody)\n      })\n\n      if (!response.ok) {\n        throw new Error(`OpenRouter API request failed: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n      const content = data.choices[0]?.message?.content\n\n      if (!content) {\n        throw new Error('No content received from OpenRouter AI service')\n      }\n\n      console.log('✅ Received response from OpenRouter AI')\n\n      // Clean the response and parse JSON\n      const cleanedContent = content.replace(/```json|```/g, '').trim()\n      return JSON.parse(cleanedContent)\n    } catch (error) {\n      console.error('❌ Error generating market analysis:', error)\n      throw new Error('Failed to generate market analysis')\n    }\n  }\n\n  static async generateProductInsights(\n    productDetails: any,\n    reviews: string[]\n  ): Promise<{\n    strengths: string[]\n    weaknesses: string[]\n    opportunities: string[]\n    threats: string[]\n    recommendations: string[]\n  }> {\n    try {\n      const requestBody = {\n        model: \"deepseek/deepseek-r1:free\",\n        messages: [{\n          role: \"user\",\n          content: `Analyze this product and its reviews to provide strategic insights:\n\n                   Product: ${productDetails.product_title}\n                   Price: ${productDetails.product_price}\n                   Rating: ${productDetails.product_star_rating}\n                   Reviews Count: ${productDetails.product_num_ratings}\n\n                   Sample Reviews: ${reviews.slice(0, 50).join('\\n')}\n\n                   Provide a SWOT analysis and recommendations in JSON format:\n                   {\n                     \"strengths\": [\"strength1\", \"strength2\", \"strength3\"],\n                     \"weaknesses\": [\"weakness1\", \"weakness2\", \"weakness3\"],\n                     \"opportunities\": [\"opportunity1\", \"opportunity2\", \"opportunity3\"],\n                     \"threats\": [\"threat1\", \"threat2\", \"threat3\"],\n                     \"recommendations\": [\"rec1\", \"rec2\", \"rec3\"]\n                   }\n\n                   IMPORTANT: Respond ONLY with valid JSON, no markdown or backticks.`\n        }]\n      }\n\n      const response = await fetch(\"https://openrouter.ai/api/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Authorization\": `Bearer ${API_KEY}`,\n          \"HTTP-Referer\": \"https://github.com/amazon-product-insights\",\n          \"X-Title\": \"Amazon Product Insights\",\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(requestBody)\n      })\n\n      if (!response.ok) {\n        throw new Error(`API request failed: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n      const content = data.choices[0]?.message?.content\n\n      if (!content) {\n        throw new Error('No content received from AI service')\n      }\n\n      // Clean the response and parse JSON\n      const cleanedContent = content.replace(/```json|```/g, '').trim()\n      return JSON.parse(cleanedContent)\n    } catch (error) {\n      console.error('Error generating product insights:', error)\n      throw new Error('Failed to generate product insights')\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,UAAU,QAAQ,GAAG,CAAC,kBAAkB;AA2EvC,MAAM;IACX,aAAa,eACX,OAAiB,EACjB,SAAsB,IAAI,EACK;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAS;gBAAO;YACzC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,uBACX,QAAe,EACf,KAAa,EACkB;QAC/B,IAAI;YACF,QAAQ,GAAG,CAAC,+DAA+D;YAC3E,QAAQ,GAAG,CAAC,2BAA2B,SAAS,MAAM;YAEtD,sCAAsC;YACtC,IAAI,CAAC,WAAW,YAAY,gCAAgC;gBAC1D,QAAQ,GAAG,CAAC;gBACZ,MAAM,mBAAmB,SAAS,MAAM,GAAG,IAAI,SAAS,SAAS,MAAM,GAAG,IAAI,WAAW;gBACzF,MAAM,aAAa;oBAAC;oBAAc;oBAAc;iBAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;gBAExF,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,WAAW,EAAE,aAAa,EAAE,QAAQ,KAAK,OAAO,MAAM,KAAK,SAAS,MAAM;gBACxI,MAAM,WAAW,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,WAAW,EAAE,aAAa,EAAE,QAAQ,KAAK,OAAO;gBAC/F,MAAM,WAAW,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,WAAW,EAAE,aAAa,EAAE,QAAQ,KAAK,OAAO;gBAE/F,MAAM,eAAqC;oBACzC,aAAa,CAAC,kBAAkB,EAAE,SAAS,MAAM,GAAG,IAAI,gBAAgB,EAAE,KAAK,KAAK,CAAC,WAAW,SAAS,MAAM,GAAG,MAAM,iBAAiB,CAAC;oBAC1I,mBAAmB;oBACnB,mBAAmB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBACpD,cAAc;wBACZ,CAAC,IAAI,EAAE,MAAM,yDAAyD,EAAE,SAAS,MAAM,CAAC,kBAAkB,CAAC;wBAC3G,CAAC,yBAAyB,EAAE,SAAS,KAAK,EAAE,SAAS,4BAA4B,CAAC;wBAClF,CAAC,4EAA4E,CAAC;wBAC9E,CAAC,aAAa,EAAE,WAAW,yBAAyB,EAAE,eAAe,eAAe,mBAAmB,eAAe,eAAe,sBAAsB,oBAAoB;wBAC/K,CAAC,oCAAoC,EAAE,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,EAAE,MAAM,CAAC,QAAQ,EAAE,SAAS,MAAM,CAAC,mCAAmC,CAAC;qBACpJ;oBACD,qBAAqB;wBACnB,CAAC,YAAY,EAAE,KAAK,KAAK,CAAC,WAAW,KAAK,EAAE,EAAE,KAAK,KAAK,CAAC,WAAW,KAAK,qCAAqC,CAAC;wBAC/G,CAAC,wEAAwE,CAAC;wBAC1E,CAAC,uEAAuE,CAAC;wBACzE,CAAC,sDAAsD,EAAE,MAAM,SAAS,CAAC;wBACzE,CAAC,+DAA+D,CAAC;qBAClE;oBACD,gBAAgB;wBACd,eAAe;wBACf,aAAa;4BAAE,KAAK;4BAAU,KAAK;wBAAS;wBAC5C,aAAa;wBACb,gBAAgB;4BACd,SAAS,KAAK,KAAK,CAAC,WAAW;4BAC/B,UAAU,KAAK,KAAK,CAAC,WAAW;4BAChC,SAAS,KAAK,KAAK,CAAC,WAAW;wBACjC;oBACF;oBACA,qBAAqB;wBACnB,YAAY;4BAAC;4BAAS;4BAAW;4BAAQ;4BAAO;yBAAQ,CAAC,KAAK,CAAC,GAAG;wBAClE,gBAAgB;4BACd;gCACE,OAAO;gCACP,cAAc;gCACd,gBAAgB;oCAAC;oCAAqB;oCAAwB;iCAAkB;4BAClF;4BACA;gCACE,OAAO;gCACP,cAAc;gCACd,gBAAgB;oCAAC;oCAAuB;oCAAc;iCAAmB;4BAC3E;yBACD;wBACD,kBAAkB;4BAChB;4BACA;4BACA;yBACD;oBACH;oBACA,mBAAmB;wBACjB,qBAAqB;4BAAC;4BAAoB;4BAAiB;yBAA6B;wBACxF,iBAAiB;4BAAC;4BAA6B;4BAA6B;yBAA8B;wBAC1G,aAAa;4BAAC;4BAAiB;4BAAuB;yBAA4B;wBAClF,oBAAoB;oBACtB;oBACA,eAAe;wBACb,mBAAmB;wBACnB,mBAAmB;wBACnB,mBAAmB;4BAAC;4BAAyB;4BAAkB;yBAAuB;wBACtF,iBAAiB;oBACnB;oBACA,iBAAiB;wBACf,cAAc;4BAAC;4BAA4B;4BAA4B;yBAA0B;wBACjG,qBAAqB;4BAAC;4BAAuB;4BAAc;yBAAkB;wBAC7E,mBAAmB;4BAAC;4BAAqB;4BAAiC;yBAAqB;wBAC/F,YAAY;oBACd;oBACA,gBAAgB;wBACd,yBAAyB;4BACvB,KAAK,KAAK,KAAK,CAAC,WAAW;4BAC3B,KAAK,KAAK,KAAK,CAAC,WAAW;4BAC3B,SAAS;wBACX;wBACA,+BAA+B;4BAC7B;4BACA;4BACA;4BACA;yBACD;wBACD,oBAAoB;4BAAC;4BAAc;4BAA4B;4BAA2B;yBAAoB;wBAC9G,oBAAoB;oBACtB;oBACA,cAAc,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;4BAC3D,MAAM,QAAQ,IAAI;4BAClB,OAAO,QAAQ,aAAa;4BAC5B,OAAO,QAAQ,aAAa,IAAI;4BAChC,QAAQ,QAAQ,mBAAmB;4BACnC,SAAS,QAAQ,mBAAmB;4BACpC,cAAc,QAAQ,YAAY,IAAI;4BACtC,gBAAgB,QAAQ,cAAc,IAAI;4BAC1C,uBAAuB,QAAQ,IAAI,oCAAoC,QAAQ,KAAK,sCAAsC,QAAQ,KAAK,qCAAqC;4BAC5K,iBAAkB,QAAQ,IAAI,WAAW,QAAQ,IAAI,eAAe,QAAQ,KAAK,aAAa;wBAChG,CAAC;gBACH;gBACA,OAAO;YACT;YAEA,uCAAuC;YACvC,MAAM,iBAAiB,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,IAAK,CAAC;oBACrD,OAAO,EAAE,aAAa;oBACtB,OAAO,EAAE,aAAa;oBACtB,QAAQ,EAAE,mBAAmB;oBAC7B,SAAS,EAAE,mBAAmB;oBAC9B,gBAAgB,EAAE,cAAc;oBAChC,cAAc,EAAE,YAAY;gBAC9B,CAAC;YAED,MAAM,cAAc;gBAClB,OAAO;gBACP,UAAU;oBAAC;wBACT,MAAM;wBACN,SAAS,CAAC,YAAY,EAAE,MAAM;;;AAGxC,EAAE,KAAK,SAAS,CAAC,gBAAgB,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sGAyE4D,CAAC;oBAC/F;iBAAE;YACJ;YAEA,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,MAAM,iDAAiD;gBAC5E,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,SAAS;oBACpC,gBAAgB;oBAChB,WAAW;oBACX,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,UAAU,EAAE;YACzE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,UAAU,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS;YAE1C,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC;YAEZ,oCAAoC;YACpC,MAAM,iBAAiB,QAAQ,OAAO,CAAC,gBAAgB,IAAI,IAAI;YAC/D,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,wBACX,cAAmB,EACnB,OAAiB,EAOhB;QACD,IAAI;YACF,MAAM,cAAc;gBAClB,OAAO;gBACP,UAAU;oBAAC;wBACT,MAAM;wBACN,SAAS,CAAC;;4BAEQ,EAAE,eAAe,aAAa,CAAC;0BACjC,EAAE,eAAe,aAAa,CAAC;2BAC9B,EAAE,eAAe,mBAAmB,CAAC;kCAC9B,EAAE,eAAe,mBAAmB,CAAC;;mCAEpC,EAAE,QAAQ,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM;;;;;;;;;;;qFAWgB,CAAC;oBAC9E;iBAAE;YACJ;YAEA,MAAM,WAAW,MAAM,MAAM,iDAAiD;gBAC5E,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,SAAS;oBACpC,gBAAgB;oBAChB,WAAW;oBACX,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,UAAU,EAAE;YAC9D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,UAAU,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS;YAE1C,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,MAAM,iBAAiB,QAAQ,OAAO,CAAC,gBAAgB,IAAI,IAAI;YAC/D,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/lib/services/amazon-api.ts"], "sourcesContent": ["import axios from 'axios'\n\nconst RAPIDAPI_KEY = process.env.RAPIDAPI_KEY!\nconst BASE_URL = 'https://real-time-amazon-data.p.rapidapi.com'\n\nconst apiClient = axios.create({\n  baseURL: BASE_URL,\n  headers: {\n    'x-rapidapi-key': RAPIDAPI_KEY,\n    'x-rapidapi-host': 'real-time-amazon-data.p.rapidapi.com'\n  }\n})\n\nexport interface AmazonProduct {\n  asin: string\n  product_title: string\n  product_price: string\n  product_original_price?: string\n  currency: string\n  product_star_rating: string\n  product_num_ratings: number\n  product_url: string\n  product_photo: string\n  product_availability: string\n  is_best_seller: boolean\n  is_amazon_choice: boolean\n  is_prime: boolean\n  climate_pledge_friendly: boolean\n  sales_volume?: string\n  delivery?: string\n}\n\nexport interface AmazonReview {\n  review_id: string\n  review_title: string\n  review_comment: string\n  review_star_rating: number\n  review_date: string\n  review_author: string\n  review_author_avatar?: string\n  is_verified_purchase: boolean\n  helpful_vote_statement?: string\n  review_country: string\n  review_images?: string[]\n  review_videos?: string[]\n}\n\nexport interface ProductDetails {\n  asin: string\n  product_title: string\n  product_price: string\n  product_original_price?: string\n  currency: string\n  product_star_rating: string\n  product_num_ratings: number\n  product_url: string\n  product_photos: string[]\n  product_details: Record<string, any>\n  product_description: string\n  product_information: Record<string, any>\n  product_specifications: Record<string, any>\n  customers_say: {\n    keywords: Array<{\n      keyword: string\n      sentiment_score: number\n      mentions_count: number\n    }>\n  }\n  category_tree: Array<{\n    name: string\n    link: string\n  }>\n}\n\nexport class AmazonAPIService {\n  static async searchProducts(query: string, page = 1, country = 'US'): Promise<{\n    data: AmazonProduct[]\n    total_products: number\n    country: string\n    domain: string\n  }> {\n    try {\n      const response = await fetch(`/api/amazon/search?query=${encodeURIComponent(query)}&page=${page}&country=${country}`)\n      if (!response.ok) {\n        throw new Error('Failed to search products')\n      }\n      return await response.json()\n    } catch (error) {\n      console.error('Error searching products:', error)\n      throw new Error('Failed to search products')\n    }\n  }\n\n  static async getProductDetails(asin: string, country = 'US'): Promise<ProductDetails> {\n    try {\n      const response = await fetch(`/api/amazon/product/${asin}?country=${country}`)\n      if (!response.ok) {\n        throw new Error('Failed to fetch product details')\n      }\n      return await response.json()\n    } catch (error) {\n      console.error('Error fetching product details:', error)\n      throw new Error('Failed to fetch product details')\n    }\n  }\n\n  static async getProductReviews(\n    asin: string,\n    page = 1,\n    country = 'US',\n    sortBy = 'TOP_REVIEWS'\n  ): Promise<{\n    data: AmazonReview[]\n    total_reviews: number\n    country: string\n    domain: string\n  }> {\n    try {\n      const response = await fetch(`/api/amazon/reviews/${asin}?page=${page}&country=${country}&sortBy=${sortBy}`)\n      if (!response.ok) {\n        throw new Error('Failed to fetch product reviews')\n      }\n      return await response.json()\n    } catch (error) {\n      console.error('Error fetching product reviews:', error)\n      throw new Error('Failed to fetch product reviews')\n    }\n  }\n\n  static async getProductsByCategory(\n    categoryId: string,\n    page = 1,\n    country = 'US'\n  ): Promise<{\n    data: AmazonProduct[]\n    total_products: number\n    country: string\n    domain: string\n  }> {\n    try {\n      const response = await apiClient.get('/products-by-category', {\n        params: {\n          category_id: categoryId,\n          page: page.toString(),\n          country\n        }\n      })\n      return response.data\n    } catch (error) {\n      console.error('Error fetching products by category:', error)\n      throw new Error('Failed to fetch products by category')\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,eAAe,QAAQ,GAAG,CAAC,YAAY;AAC7C,MAAM,WAAW;AAEjB,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,kBAAkB;QAClB,mBAAmB;IACrB;AACF;AA+DO,MAAM;IACX,aAAa,eAAe,KAAa,EAAE,OAAO,CAAC,EAAE,UAAU,IAAI,EAKhE;QACD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,yBAAyB,EAAE,mBAAmB,OAAO,MAAM,EAAE,KAAK,SAAS,EAAE,SAAS;YACpH,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,kBAAkB,IAAY,EAAE,UAAU,IAAI,EAA2B;QACpF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,KAAK,SAAS,EAAE,SAAS;YAC7E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,kBACX,IAAY,EACZ,OAAO,CAAC,EACR,UAAU,IAAI,EACd,SAAS,aAAa,EAMrB;QACD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,KAAK,MAAM,EAAE,KAAK,SAAS,EAAE,QAAQ,QAAQ,EAAE,QAAQ;YAC3G,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,sBACX,UAAkB,EAClB,OAAO,CAAC,EACR,UAAU,IAAI,EAMb;QACD,IAAI;YACF,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,yBAAyB;gBAC5D,QAAQ;oBACN,aAAa;oBACb,MAAM,KAAK,QAAQ;oBACnB;gBACF;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/app/dashboard/market-analysis/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport {\n  TrendingUp,\n  TrendingDown,\n  BarChart3,\n  DollarSign,\n  Users,\n  Package,\n  AlertCircle,\n  CheckCircle,\n  Loader2\n} from 'lucide-react'\nimport { OpenRouterAIService, type MarketAnalysisResult } from '@/lib/services/openrouter-ai'\nimport { AmazonAPIService } from '@/lib/services/amazon-api'\n\nexport default function MarketAnalysis() {\n  const [niche, setNiche] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [analysis, setAnalysis] = useState<MarketAnalysisResult | null>(null)\n  const [selectedProduct, setSelectedProduct] = useState<any>(null)\n  const [productAnalysisLoading, setProductAnalysisLoading] = useState(false)\n\n  const handleAnalyze = async () => {\n    if (!niche.trim()) return\n\n    setLoading(true)\n    try {\n      // First, search for products in the niche - get more products for top 20 analysis\n      console.log('🔍 Searching for products with niche:', niche)\n      const searchResults = await AmazonAPIService.searchProducts(niche, 1)\n\n      console.log('📦 Search results received:', searchResults)\n      console.log('📦 Search results type:', typeof searchResults)\n      console.log('📦 Search results keys:', Object.keys(searchResults || {}))\n\n      // Handle different response structures\n      let products = []\n      if (searchResults?.data?.products && Array.isArray(searchResults.data.products)) {\n        // Real Amazon API structure: { data: { products: [...] } }\n        products = searchResults.data.products\n      } else if (searchResults?.data && Array.isArray(searchResults.data)) {\n        // Mock API structure: { data: [...] }\n        products = searchResults.data\n      } else if (Array.isArray(searchResults)) {\n        // Direct array\n        products = searchResults\n      } else if (searchResults?.products && Array.isArray(searchResults.products)) {\n        // Alternative structure: { products: [...] }\n        products = searchResults.products\n      } else {\n        console.error('❌ Unexpected response structure:', searchResults)\n        throw new Error('Invalid response structure from Amazon API')\n      }\n\n      console.log('📊 Products array:', products)\n      console.log('📊 Products count:', products.length)\n\n      if (products.length === 0) {\n        throw new Error('No products found for this niche')\n      }\n\n      // Generate market analysis with top 20 products\n      console.log('🤖 Generating market analysis for', products.length, 'products')\n      const marketAnalysis = await OpenRouterAIService.generateMarketAnalysis(\n        products.slice(0, 20), // Analyze top 20 products\n        niche\n      )\n\n      console.log('✅ Market analysis completed:', marketAnalysis)\n      setAnalysis(marketAnalysis)\n    } catch (error) {\n      console.error('❌ Market analysis failed:', error)\n      // Handle error - show toast notification\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleProductAnalysis = async (product: any) => {\n    setSelectedProduct(product)\n    setProductAnalysisLoading(true)\n\n    try {\n      console.log('🔍 Analyzing product:', product.title)\n\n      // 1. Fetch product reviews\n      console.log('📝 Fetching reviews for ASIN:', product.asin)\n      const reviewsResponse = await fetch(`/api/amazon/product-reviews?asin=${product.asin}&country=US`)\n\n      if (!reviewsResponse.ok) {\n        throw new Error('Failed to fetch product reviews')\n      }\n\n      const reviewsData = await reviewsResponse.json()\n      console.log('📝 Reviews data received:', reviewsData)\n\n      // Extract review texts for analysis\n      let reviews = []\n      if (reviewsData?.data?.reviews && Array.isArray(reviewsData.data.reviews)) {\n        reviews = reviewsData.data.reviews.map((review: any) => review.review_comment).filter(Boolean)\n      } else if (Array.isArray(reviewsData?.reviews)) {\n        reviews = reviewsData.reviews.map((review: any) => review.review_comment).filter(Boolean)\n      }\n\n      console.log('📝 Extracted', reviews.length, 'review texts for analysis')\n\n      if (reviews.length > 0) {\n        // 2. Analyze reviews using AI\n        console.log('🤖 Analyzing reviews with AI...')\n        const reviewAnalysis = await OpenRouterAIService.analyzeReviews(reviews.slice(0, 20)) // Analyze top 20 reviews\n        console.log('✅ Review analysis completed:', reviewAnalysis)\n\n        // 3. Show results (you could display this in a modal or new section)\n        alert(`Product Analysis Complete!\\n\\nPros: ${reviewAnalysis.pros?.map(p => p.text).join(', ') || 'N/A'}\\n\\nCons: ${reviewAnalysis.cons?.map(c => c.text).join(', ') || 'N/A'}\\n\\nSentiment Score: ${reviewAnalysis.sentiment_score || 'N/A'}/5`)\n      } else {\n        alert('No reviews found for analysis')\n      }\n\n    } catch (error) {\n      console.error('❌ Product analysis failed:', error)\n      alert('Failed to analyze product. Please try again.')\n    } finally {\n      setProductAnalysisLoading(false)\n    }\n  }\n\n  const getCompetitionColor = (level: string) => {\n    switch (level) {\n      case 'low': return 'text-green-600 bg-green-100'\n      case 'medium': return 'text-yellow-600 bg-yellow-100'\n      case 'high': return 'text-red-600 bg-red-100'\n      default: return 'text-gray-600 bg-gray-100'\n    }\n  }\n\n  const getOpportunityColor = (score: number) => {\n    if (score >= 70) return 'text-green-600'\n    if (score >= 40) return 'text-yellow-600'\n    return 'text-red-600'\n  }\n\n  const getTrendIcon = (trend: string) => {\n    switch (trend) {\n      case 'increasing': return <TrendingUp className=\"w-4 h-4 text-green-600\" />\n      case 'decreasing': return <TrendingDown className=\"w-4 h-4 text-red-600\" />\n      default: return <BarChart3 className=\"w-4 h-4 text-gray-600\" />\n    }\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Market Analysis</h1>\n        <p className=\"text-gray-600 mt-2\">\n          Analyze market opportunities and competition for any niche on Amazon.\n        </p>\n      </div>\n\n      {/* Analysis Input */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Niche Market Analysis</CardTitle>\n          <CardDescription>\n            Enter a product niche or category to get comprehensive market insights\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex gap-4\">\n            <Input\n              placeholder=\"e.g., wireless earbuds, yoga mats, coffee makers...\"\n              value={niche}\n              onChange={(e) => setNiche(e.target.value)}\n              onKeyPress={(e) => e.key === 'Enter' && handleAnalyze()}\n              className=\"flex-1\"\n            />\n            <Button\n              onClick={handleAnalyze}\n              disabled={loading || !niche.trim()}\n              className=\"px-8\"\n            >\n              {loading ? (\n                <Loader2 className=\"w-4 h-4 animate-spin\" />\n              ) : (\n                <BarChart3 className=\"w-4 h-4\" />\n              )}\n              {loading ? 'Analyzing...' : 'Analyze Market'}\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Analysis Results */}\n      {analysis && (\n        <div className=\"space-y-6\">\n          {/* Market Overview */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Market Size</CardTitle>\n                <Package className=\"w-4 h-4 text-blue-400\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-sm font-bold\">{analysis.market_size}</div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Competition Level</CardTitle>\n                <Users className=\"w-4 h-4 text-purple-400\" />\n              </CardHeader>\n              <CardContent>\n                <div className={`text-lg font-bold capitalize px-3 py-1 rounded-full inline-block ${getCompetitionColor(analysis.competition_level)}`}>\n                  {analysis.competition_level}\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Opportunity Score</CardTitle>\n                <TrendingUp className=\"w-4 h-4 text-green-400\" />\n              </CardHeader>\n              <CardContent>\n                <div className={`text-2xl font-bold ${getOpportunityColor(analysis.opportunity_score)}`}>\n                  {analysis.opportunity_score}/100\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Average Price</CardTitle>\n                <DollarSign className=\"w-4 h-4 text-green-400\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">${analysis.price_analysis.average_price}</div>\n                <div className=\"flex items-center text-sm text-gray-600 mt-1\">\n                  {getTrendIcon(analysis.price_analysis.price_trend)}\n                  <span className=\"ml-1 capitalize\">{analysis.price_analysis.price_trend}</span>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Enhanced Price Analysis */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Price Analysis & Profit Margins</CardTitle>\n              <CardDescription>\n                Comprehensive pricing insights and profit potential for the {niche} market\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid md:grid-cols-2 gap-8\">\n                <div>\n                  <h4 className=\"font-semibold mb-4\">Price Range</h4>\n                  <div className=\"grid grid-cols-3 gap-4\">\n                    <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                      <div className=\"text-xl font-bold text-gray-900\">\n                        ${analysis.price_analysis.price_range.min}\n                      </div>\n                      <div className=\"text-sm text-gray-600\">Minimum</div>\n                    </div>\n                    <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n                      <div className=\"text-xl font-bold text-blue-900\">\n                        ${analysis.price_analysis.average_price}\n                      </div>\n                      <div className=\"text-sm text-blue-600\">Average</div>\n                    </div>\n                    <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                      <div className=\"text-xl font-bold text-gray-900\">\n                        ${analysis.price_analysis.price_range.max}\n                      </div>\n                      <div className=\"text-sm text-gray-600\">Maximum</div>\n                    </div>\n                  </div>\n                </div>\n                {analysis.price_analysis.profit_margins && (\n                  <div>\n                    <h4 className=\"font-semibold mb-4\">Profit Margins</h4>\n                    <div className=\"grid grid-cols-3 gap-4\">\n                      <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n                        <div className=\"text-xl font-bold text-green-900\">\n                          ${analysis.price_analysis.profit_margins.low_end}\n                        </div>\n                        <div className=\"text-sm text-green-600\">Low End</div>\n                      </div>\n                      <div className=\"text-center p-4 bg-green-100 rounded-lg\">\n                        <div className=\"text-xl font-bold text-green-900\">\n                          ${analysis.price_analysis.profit_margins.average}\n                        </div>\n                        <div className=\"text-sm text-green-600\">Average</div>\n                      </div>\n                      <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n                        <div className=\"text-xl font-bold text-green-900\">\n                          ${analysis.price_analysis.profit_margins.high_end}\n                        </div>\n                        <div className=\"text-sm text-green-600\">High End</div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Competitor Analysis */}\n          {analysis.competitor_analysis && (\n            <Card>\n              <CardHeader>\n                <CardTitle>Competitor Analysis</CardTitle>\n                <CardDescription>\n                  Key players and competitive landscape in the {niche} market\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid md:grid-cols-2 gap-8\">\n                  <div>\n                    <h4 className=\"font-semibold mb-4\">Top Brands</h4>\n                    <div className=\"space-y-2\">\n                      {analysis.competitor_analysis.top_brands.map((brand, index) => (\n                        <div key={index} className=\"flex items-center gap-3 p-3 bg-gray-50 rounded-lg\">\n                          <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm\">\n                            {index + 1}\n                          </div>\n                          <span className=\"font-medium\">{brand}</span>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold mb-4\">Market Leaders</h4>\n                    <div className=\"space-y-4\">\n                      {analysis.competitor_analysis.market_leaders.map((leader, index) => (\n                        <div key={index} className=\"p-4 border rounded-lg\">\n                          <div className=\"flex justify-between items-center mb-2\">\n                            <h5 className=\"font-semibold\">{leader.brand}</h5>\n                            <span className=\"text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded\">\n                              {leader.market_share}% share\n                            </span>\n                          </div>\n                          <div className=\"text-sm text-gray-600\">\n                            <strong>Key Advantages:</strong> {leader.key_advantages.join(', ')}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n                {analysis.competitor_analysis.competitive_gaps.length > 0 && (\n                  <div className=\"mt-6\">\n                    <h4 className=\"font-semibold mb-4\">Competitive Gaps & Opportunities</h4>\n                    <div className=\"grid md:grid-cols-2 gap-4\">\n                      {analysis.competitor_analysis.competitive_gaps.map((gap, index) => (\n                        <div key={index} className=\"flex items-start gap-3 p-3 bg-yellow-50 rounded-lg\">\n                          <AlertCircle className=\"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\" />\n                          <p className=\"text-yellow-900\">{gap}</p>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Key Insights */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Key Market Insights</CardTitle>\n              <CardDescription>\n                Important findings and observations about the {niche} market\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {analysis.key_insights.map((insight, index) => (\n                  <div key={index} className=\"flex items-start gap-3 p-3 bg-blue-50 rounded-lg\">\n                    <CheckCircle className=\"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0\" />\n                    <p className=\"text-blue-900\">{insight}</p>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Recommended Actions */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Recommended Actions</CardTitle>\n              <CardDescription>\n                Strategic recommendations based on the market analysis\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {analysis.recommended_actions.map((action, index) => (\n                  <div key={index} className=\"flex items-start gap-3 p-3 bg-green-50 rounded-lg\">\n                    <AlertCircle className=\"w-5 h-5 text-green-600 mt-0.5 flex-shrink-0\" />\n                    <p className=\"text-green-900\">{action}</p>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Top 20 Products */}\n          {analysis.top_products && analysis.top_products.length > 0 && (\n            <Card>\n              <CardHeader>\n                <CardTitle>Top 20 Products in {niche}</CardTitle>\n                <CardDescription>\n                  Best-performing products with detailed analysis - click any product for review insights\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                  {analysis.top_products.map((product, index) => (\n                    <div\n                      key={product.asin}\n                      className=\"border rounded-lg p-4 hover:shadow-lg transition-shadow cursor-pointer\"\n                      onClick={() => handleProductAnalysis(product)}\n                    >\n                      <div className=\"flex items-start justify-between mb-3\">\n                        <div className=\"flex items-center gap-2\">\n                          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${\n                            product.market_position === 'leader' ? 'bg-yellow-100 text-yellow-800' :\n                            product.market_position === 'challenger' ? 'bg-blue-100 text-blue-800' :\n                            product.market_position === 'follower' ? 'bg-gray-100 text-gray-800' :\n                            'bg-purple-100 text-purple-800'\n                          }`}>\n                            {index + 1}\n                          </div>\n                          {product.is_best_seller && (\n                            <span className=\"bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full\">\n                              Best Seller\n                            </span>\n                          )}\n                        </div>\n                        <div className={`text-xs px-2 py-1 rounded-full ${\n                          product.market_position === 'leader' ? 'bg-yellow-100 text-yellow-800' :\n                          product.market_position === 'challenger' ? 'bg-blue-100 text-blue-800' :\n                          product.market_position === 'follower' ? 'bg-gray-100 text-gray-800' :\n                          'bg-purple-100 text-purple-800'\n                        }`}>\n                          {product.market_position}\n                        </div>\n                      </div>\n\n                      <h4 className=\"font-semibold text-sm mb-2 line-clamp-2\">\n                        {product.title}\n                      </h4>\n\n                      <div className=\"space-y-2 text-sm\">\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Price:</span>\n                          <span className=\"font-semibold\">{product.price}</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Rating:</span>\n                          <span className=\"flex items-center gap-1\">\n                            <span className=\"font-semibold\">{product.rating}</span>\n                            <span className=\"text-gray-500\">({product.reviews})</span>\n                          </span>\n                        </div>\n                        {product.sales_volume && (\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-600\">Sales:</span>\n                            <span className=\"text-sm\">{product.sales_volume}</span>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"mt-3 p-2 bg-gray-50 rounded text-xs\">\n                        <strong>Advantage:</strong> {product.competitive_advantage}\n                      </div>\n\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        className=\"w-full mt-3\"\n                        onClick={(e) => {\n                          e.stopPropagation()\n                          handleProductAnalysis(product)\n                        }}\n                      >\n                        {productAnalysisLoading && selectedProduct?.asin === product.asin ? (\n                          <Loader2 className=\"w-4 h-4 animate-spin\" />\n                        ) : (\n                          'Analyze Reviews'\n                        )}\n                      </Button>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      )}\n\n      {/* Empty State */}\n      {!analysis && !loading && (\n        <Card className=\"text-center py-12\">\n          <CardContent>\n            <BarChart3 className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n              No Analysis Yet\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Enter a product niche above to get comprehensive market insights and competitive analysis.\n            </p>\n            <div className=\"flex justify-center gap-4\">\n              <Button variant=\"outline\">\n                <Package className=\"w-4 h-4 mr-2\" />\n                View Examples\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAlBA;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,MAAM,gBAAgB;QACpB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,WAAW;QACX,IAAI;YACF,kFAAkF;YAClF,QAAQ,GAAG,CAAC,yCAAyC;YACrD,MAAM,gBAAgB,MAAM,uIAAA,CAAA,mBAAgB,CAAC,cAAc,CAAC,OAAO;YAEnE,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,QAAQ,GAAG,CAAC,2BAA2B,OAAO;YAC9C,QAAQ,GAAG,CAAC,2BAA2B,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAErE,uCAAuC;YACvC,IAAI,WAAW,EAAE;YACjB,IAAI,eAAe,MAAM,YAAY,MAAM,OAAO,CAAC,cAAc,IAAI,CAAC,QAAQ,GAAG;gBAC/E,2DAA2D;gBAC3D,WAAW,cAAc,IAAI,CAAC,QAAQ;YACxC,OAAO,IAAI,eAAe,QAAQ,MAAM,OAAO,CAAC,cAAc,IAAI,GAAG;gBACnE,sCAAsC;gBACtC,WAAW,cAAc,IAAI;YAC/B,OAAO,IAAI,MAAM,OAAO,CAAC,gBAAgB;gBACvC,eAAe;gBACf,WAAW;YACb,OAAO,IAAI,eAAe,YAAY,MAAM,OAAO,CAAC,cAAc,QAAQ,GAAG;gBAC3E,6CAA6C;gBAC7C,WAAW,cAAc,QAAQ;YACnC,OAAO;gBACL,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC,sBAAsB;YAClC,QAAQ,GAAG,CAAC,sBAAsB,SAAS,MAAM;YAEjD,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,MAAM,IAAI,MAAM;YAClB;YAEA,gDAAgD;YAChD,QAAQ,GAAG,CAAC,qCAAqC,SAAS,MAAM,EAAE;YAClE,MAAM,iBAAiB,MAAM,0IAAA,CAAA,sBAAmB,CAAC,sBAAsB,CACrE,SAAS,KAAK,CAAC,GAAG,KAClB;YAGF,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,yCAAyC;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,mBAAmB;QACnB,0BAA0B;QAE1B,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB,QAAQ,KAAK;YAElD,2BAA2B;YAC3B,QAAQ,GAAG,CAAC,iCAAiC,QAAQ,IAAI;YACzD,MAAM,kBAAkB,MAAM,MAAM,CAAC,iCAAiC,EAAE,QAAQ,IAAI,CAAC,WAAW,CAAC;YAEjG,IAAI,CAAC,gBAAgB,EAAE,EAAE;gBACvB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,cAAc,MAAM,gBAAgB,IAAI;YAC9C,QAAQ,GAAG,CAAC,6BAA6B;YAEzC,oCAAoC;YACpC,IAAI,UAAU,EAAE;YAChB,IAAI,aAAa,MAAM,WAAW,MAAM,OAAO,CAAC,YAAY,IAAI,CAAC,OAAO,GAAG;gBACzE,UAAU,YAAY,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAgB,OAAO,cAAc,EAAE,MAAM,CAAC;YACxF,OAAO,IAAI,MAAM,OAAO,CAAC,aAAa,UAAU;gBAC9C,UAAU,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,SAAgB,OAAO,cAAc,EAAE,MAAM,CAAC;YACnF;YAEA,QAAQ,GAAG,CAAC,gBAAgB,QAAQ,MAAM,EAAE;YAE5C,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,8BAA8B;gBAC9B,QAAQ,GAAG,CAAC;gBACZ,MAAM,iBAAiB,MAAM,0IAAA,CAAA,sBAAmB,CAAC,cAAc,CAAC,QAAQ,KAAK,CAAC,GAAG,KAAK,yBAAyB;;gBAC/G,QAAQ,GAAG,CAAC,gCAAgC;gBAE5C,qEAAqE;gBACrE,MAAM,CAAC,oCAAoC,EAAE,eAAe,IAAI,EAAE,IAAI,CAAA,IAAK,EAAE,IAAI,EAAE,KAAK,SAAS,MAAM,UAAU,EAAE,eAAe,IAAI,EAAE,IAAI,CAAA,IAAK,EAAE,IAAI,EAAE,KAAK,SAAS,MAAM,qBAAqB,EAAE,eAAe,eAAe,IAAI,MAAM,EAAE,CAAC;YACjP,OAAO;gBACL,MAAM;YACR;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR,SAAU;YACR,0BAA0B;QAC5B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAc,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAc,qBAAO,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YAClD;gBAAS,qBAAO,8OAAC,kNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QACvC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAMpC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;oCACxC,WAAU;;;;;;8CAEZ,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,WAAW,CAAC,MAAM,IAAI;oCAChC,WAAU;;wCAET,wBACC,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,8OAAC,kNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAEtB,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;YAOnC,0BACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;kDAErB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAqB,SAAS,WAAW;;;;;;;;;;;;;;;;;0CAI5D,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAW,CAAC,iEAAiE,EAAE,oBAAoB,SAAS,iBAAiB,GAAG;sDAClI,SAAS,iBAAiB;;;;;;;;;;;;;;;;;0CAKjC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAW,CAAC,mBAAmB,EAAE,oBAAoB,SAAS,iBAAiB,GAAG;;gDACpF,SAAS,iBAAiB;gDAAC;;;;;;;;;;;;;;;;;;0CAKlC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;;oDAAqB;oDAAE,SAAS,cAAc,CAAC,aAAa;;;;;;;0DAC3E,8OAAC;gDAAI,WAAU;;oDACZ,aAAa,SAAS,cAAc,CAAC,WAAW;kEACjD,8OAAC;wDAAK,WAAU;kEAAmB,SAAS,cAAc,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9E,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;;4CAAC;4CAC8C;4CAAM;;;;;;;;;;;;;0CAGvE,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEAAkC;wEAC7C,SAAS,cAAc,CAAC,WAAW,CAAC,GAAG;;;;;;;8EAE3C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEAAkC;wEAC7C,SAAS,cAAc,CAAC,aAAa;;;;;;;8EAEzC,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEAAkC;wEAC7C,SAAS,cAAc,CAAC,WAAW,CAAC,GAAG;;;;;;;8EAE3C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;wCAI5C,SAAS,cAAc,CAAC,cAAc,kBACrC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEAAmC;wEAC9C,SAAS,cAAc,CAAC,cAAc,CAAC,OAAO;;;;;;;8EAElD,8OAAC;oEAAI,WAAU;8EAAyB;;;;;;;;;;;;sEAE1C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEAAmC;wEAC9C,SAAS,cAAc,CAAC,cAAc,CAAC,OAAO;;;;;;;8EAElD,8OAAC;oEAAI,WAAU;8EAAyB;;;;;;;;;;;;sEAE1C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEAAmC;wEAC9C,SAAS,cAAc,CAAC,cAAc,CAAC,QAAQ;;;;;;;8EAEnD,8OAAC;oEAAI,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUrD,SAAS,mBAAmB,kBAC3B,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;;4CAAC;4CAC+B;4CAAM;;;;;;;;;;;;;0CAGxD,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAI,WAAU;kEACZ,SAAS,mBAAmB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,sBACnD,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC;wEAAI,WAAU;kFACZ,QAAQ;;;;;;kFAEX,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;+DAJvB;;;;;;;;;;;;;;;;0DAShB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAI,WAAU;kEACZ,SAAS,mBAAmB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACxD,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAG,WAAU;0FAAiB,OAAO,KAAK;;;;;;0FAC3C,8OAAC;gFAAK,WAAU;;oFACb,OAAO,YAAY;oFAAC;;;;;;;;;;;;;kFAGzB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;0FAAO;;;;;;4EAAwB;4EAAE,OAAO,cAAc,CAAC,IAAI,CAAC;;;;;;;;+DARvD;;;;;;;;;;;;;;;;;;;;;;oCAejB,SAAS,mBAAmB,CAAC,gBAAgB,CAAC,MAAM,GAAG,mBACtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAI,WAAU;0DACZ,SAAS,mBAAmB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,sBACvD,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;gEAAE,WAAU;0EAAmB;;;;;;;uDAFxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAaxB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;;4CAAC;4CACgC;4CAAM;;;;;;;;;;;;;0CAGzD,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,SAAS,YAAY,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnC,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAE,WAAU;8DAAiB;;;;;;;2CAFtB;;;;;;;;;;;;;;;;;;;;;kCAUlB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,SAAS,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzC,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAE,WAAU;8DAAkB;;;;;;;2CAFvB;;;;;;;;;;;;;;;;;;;;;oBAUjB,SAAS,YAAY,IAAI,SAAS,YAAY,CAAC,MAAM,GAAG,mBACvD,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;;4CAAC;4CAAoB;;;;;;;kDAC/B,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,SAAS,YAAY,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnC,8OAAC;4CAEC,WAAU;4CACV,SAAS,IAAM,sBAAsB;;8DAErC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAW,CAAC,wEAAwE,EACvF,QAAQ,eAAe,KAAK,WAAW,kCACvC,QAAQ,eAAe,KAAK,eAAe,8BAC3C,QAAQ,eAAe,KAAK,aAAa,8BACzC,iCACA;8EACC,QAAQ;;;;;;gEAEV,QAAQ,cAAc,kBACrB,8OAAC;oEAAK,WAAU;8EAA+D;;;;;;;;;;;;sEAKnF,8OAAC;4DAAI,WAAW,CAAC,+BAA+B,EAC9C,QAAQ,eAAe,KAAK,WAAW,kCACvC,QAAQ,eAAe,KAAK,eAAe,8BAC3C,QAAQ,eAAe,KAAK,aAAa,8BACzC,iCACA;sEACC,QAAQ,eAAe;;;;;;;;;;;;8DAI5B,8OAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAGhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAiB,QAAQ,KAAK;;;;;;;;;;;;sEAEhD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;;sFACd,8OAAC;4EAAK,WAAU;sFAAiB,QAAQ,MAAM;;;;;;sFAC/C,8OAAC;4EAAK,WAAU;;gFAAgB;gFAAE,QAAQ,OAAO;gFAAC;;;;;;;;;;;;;;;;;;;wDAGrD,QAAQ,YAAY,kBACnB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAW,QAAQ,YAAY;;;;;;;;;;;;;;;;;;8DAKrD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAO;;;;;;wDAAmB;wDAAE,QAAQ,qBAAqB;;;;;;;8DAG5D,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,sBAAsB;oDACxB;8DAEC,0BAA0B,iBAAiB,SAAS,QAAQ,IAAI,iBAC/D,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;+DAEnB;;;;;;;2CAtEC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;YAmFhC,CAAC,YAAY,CAAC,yBACb,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;;sCACV,8OAAC,kNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpD", "debugId": null}}]}