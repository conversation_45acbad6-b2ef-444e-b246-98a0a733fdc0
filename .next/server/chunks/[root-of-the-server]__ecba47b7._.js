module.exports = {

"[project]/.next-internal/server/app/api/ai/market-analysis/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/services/openrouter-ai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OpenRouterAIService": (()=>OpenRouterAIService)
});
const API_KEY = process.env.OPENROUTER_API_KEY;
class OpenRouterAIService {
    static async analyzeReviews(reviews, locale = 'en') {
        try {
            const response = await fetch('/api/ai/analyze-reviews', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    reviews,
                    locale
                })
            });
            if (!response.ok) {
                throw new Error('Failed to analyze reviews');
            }
            return await response.json();
        } catch (error) {
            console.error('Error analyzing reviews:', error);
            throw new Error('Failed to analyze reviews');
        }
    }
    static async generateMarketAnalysis(products, niche) {
        try {
            const response = await fetch('/api/ai/market-analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    products,
                    niche
                })
            });
            if (!response.ok) {
                throw new Error('Failed to generate market analysis');
            }
            return await response.json();
        } catch (error) {
            console.error('Error generating market analysis:', error);
            throw new Error('Failed to generate market analysis');
        }
    }
    static async generateProductInsights(productDetails, reviews) {
        try {
            const requestBody = {
                model: "deepseek/deepseek-r1:free",
                messages: [
                    {
                        role: "user",
                        content: `Analyze this product and its reviews to provide strategic insights:

                   Product: ${productDetails.product_title}
                   Price: ${productDetails.product_price}
                   Rating: ${productDetails.product_star_rating}
                   Reviews Count: ${productDetails.product_num_ratings}

                   Sample Reviews: ${reviews.slice(0, 50).join('\n')}

                   Provide a SWOT analysis and recommendations in JSON format:
                   {
                     "strengths": ["strength1", "strength2", "strength3"],
                     "weaknesses": ["weakness1", "weakness2", "weakness3"],
                     "opportunities": ["opportunity1", "opportunity2", "opportunity3"],
                     "threats": ["threat1", "threat2", "threat3"],
                     "recommendations": ["rec1", "rec2", "rec3"]
                   }

                   IMPORTANT: Respond ONLY with valid JSON, no markdown or backticks.`
                    }
                ]
            };
            const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
                method: "POST",
                headers: {
                    "Authorization": `Bearer ${API_KEY}`,
                    "HTTP-Referer": "https://github.com/amazon-product-insights",
                    "X-Title": "Amazon Product Insights",
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(requestBody)
            });
            if (!response.ok) {
                throw new Error(`API request failed: ${response.statusText}`);
            }
            const data = await response.json();
            const content = data.choices[0]?.message?.content;
            if (!content) {
                throw new Error('No content received from AI service');
            }
            // Clean the response and parse JSON
            const cleanedContent = content.replace(/```json|```/g, '').trim();
            return JSON.parse(cleanedContent);
        } catch (error) {
            console.error('Error generating product insights:', error);
            throw new Error('Failed to generate product insights');
        }
    }
}
}}),
"[project]/src/app/api/ai/market-analysis/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$openrouter$2d$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/openrouter-ai.ts [app-route] (ecmascript)");
;
;
const API_KEY = process.env.OPENROUTER_API_KEY;
async function POST(request) {
    try {
        const body = await request.json();
        const { products, niche } = body;
        console.log('🤖 Market analysis request received for niche:', niche);
        console.log('📊 Products count:', products?.length);
        if (!products || !Array.isArray(products) || products.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Products array is required and must not be empty'
            }, {
                status: 400
            });
        }
        if (!niche || typeof niche !== 'string') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Niche parameter is required and must be a string'
            }, {
                status: 400
            });
        }
        // If no API key, return mock analysis
        if (!API_KEY || API_KEY === 'your_openrouter_api_key_here') {
            console.log('⚠️ No valid OpenRouter API key found, returning mock analysis');
            const mockAnalysis = {
                market_size: `Large market with ${products.length * 100}+ products`,
                competition_level: products.length > 5 ? 'high' : products.length > 2 ? 'medium' : 'low',
                opportunity_score: Math.floor(Math.random() * 40) + 60,
                key_insights: [
                    `The ${niche} market shows strong demand with consistent sales`,
                    `Price points range from $${Math.min(...products.map((p)=>parseFloat(p.product_price?.replace('$', '') || '0')))} to $${Math.max(...products.map((p)=>parseFloat(p.product_price?.replace('$', '') || '0')))}`,
                    `Customer satisfaction is generally high with average ratings above 4.0`
                ],
                recommended_actions: [
                    `Focus on products in the $${Math.floor(Math.random() * 20) + 20}-$${Math.floor(Math.random() * 30) + 40} price range`,
                    `Emphasize quality and customer service to compete effectively`,
                    `Consider seasonal trends and inventory management for ${niche} products`
                ],
                price_analysis: {
                    average_price: Math.floor(products.reduce((sum, p)=>sum + parseFloat(p.product_price?.replace('$', '') || '0'), 0) / products.length),
                    price_range: {
                        min: Math.min(...products.map((p)=>parseFloat(p.product_price?.replace('$', '') || '0'))),
                        max: Math.max(...products.map((p)=>parseFloat(p.product_price?.replace('$', '') || '0')))
                    },
                    price_trend: [
                        'increasing',
                        'decreasing',
                        'stable'
                    ][Math.floor(Math.random() * 3)]
                }
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(mockAnalysis);
        }
        const analysis = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$openrouter$2d$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OpenRouterAIService"].generateMarketAnalysis(products, niche);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(analysis);
    } catch (error) {
        console.error('Market analysis API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate market analysis'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__ecba47b7._.js.map