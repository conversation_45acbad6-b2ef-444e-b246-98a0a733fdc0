{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/app/api/amazon/product-reviews/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport axios from 'axios'\n\nconst RAPIDAPI_KEY = process.env.RAPIDAPI_KEY!\nconst BASE_URL = 'https://real-time-amazon-data.p.rapidapi.com'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const asin = searchParams.get('asin')\n    const country = searchParams.get('country') || 'US'\n\n    if (!asin) {\n      return NextResponse.json(\n        { error: 'ASIN parameter is required' },\n        { status: 400 }\n      )\n    }\n\n    console.log('🔍 Fetching reviews for ASIN:', asin)\n    \n    // If no API key, return mock reviews for testing\n    if (!RAPIDAPI_KEY || RAPIDAPI_KEY === 'your_rapidapi_key_here') {\n      console.log('⚠️ No valid API key found, returning mock reviews')\n      const mockReviews = {\n        data: {\n          reviews: [\n            {\n              review_id: '1',\n              review_title: 'Great product!',\n              review_comment: 'This product exceeded my expectations. Great quality and fast delivery.',\n              review_star_rating: '5',\n              review_date: '2024-01-15',\n              reviewer_name: '<PERSON>',\n              verified_purchase: true,\n              helpful_vote_statement: '5 people found this helpful'\n            },\n            {\n              review_id: '2',\n              review_title: 'Good value for money',\n              review_comment: 'Decent product for the price. Some minor issues but overall satisfied.',\n              review_star_rating: '4',\n              review_date: '2024-01-10',\n              reviewer_name: 'Sarah M.',\n              verified_purchase: true,\n              helpful_vote_statement: '3 people found this helpful'\n            },\n            {\n              review_id: '3',\n              review_title: 'Could be better',\n              review_comment: 'The product works but has some quality issues. Customer service was helpful though.',\n              review_star_rating: '3',\n              review_date: '2024-01-05',\n              reviewer_name: 'Mike R.',\n              verified_purchase: true,\n              helpful_vote_statement: '2 people found this helpful'\n            }\n          ],\n          total_reviews: 1247,\n          average_rating: 4.2\n        }\n      }\n      return NextResponse.json(mockReviews)\n    }\n\n    const response = await axios.get(`${BASE_URL}/product-reviews`, {\n      params: {\n        asin,\n        country,\n        sort_by: 'TOP_REVIEWS',\n        star_rating: 'ALL',\n        verified_purchases_only: 'false',\n        images_or_videos_only: 'false',\n        current_format_only: 'false'\n      },\n      headers: {\n        'x-rapidapi-key': RAPIDAPI_KEY,\n        'x-rapidapi-host': 'real-time-amazon-data.p.rapidapi.com'\n      }\n    })\n    \n    console.log('📦 Reviews API response status:', response.status)\n    console.log('📦 Reviews API response data keys:', Object.keys(response.data || {}))\n    \n    return NextResponse.json(response.data)\n  } catch (error) {\n    console.error('Reviews API error:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch product reviews' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,eAAe,QAAQ,GAAG,CAAC,YAAY;AAC7C,MAAM,WAAW;AAEV,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,UAAU,aAAa,GAAG,CAAC,cAAc;QAE/C,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,iCAAiC;QAE7C,iDAAiD;QACjD,IAAI,CAAC,gBAAgB,iBAAiB,0BAA0B;YAC9D,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc;gBAClB,MAAM;oBACJ,SAAS;wBACP;4BACE,WAAW;4BACX,cAAc;4BACd,gBAAgB;4BAChB,oBAAoB;4BACpB,aAAa;4BACb,eAAe;4BACf,mBAAmB;4BACnB,wBAAwB;wBAC1B;wBACA;4BACE,WAAW;4BACX,cAAc;4BACd,gBAAgB;4BAChB,oBAAoB;4BACpB,aAAa;4BACb,eAAe;4BACf,mBAAmB;4BACnB,wBAAwB;wBAC1B;wBACA;4BACE,WAAW;4BACX,cAAc;4BACd,gBAAgB;4BAChB,oBAAoB;4BACpB,aAAa;4BACb,eAAe;4BACf,mBAAmB;4BACnB,wBAAwB;wBAC1B;qBACD;oBACD,eAAe;oBACf,gBAAgB;gBAClB;YACF;YACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,SAAS,gBAAgB,CAAC,EAAE;YAC9D,QAAQ;gBACN;gBACA;gBACA,SAAS;gBACT,aAAa;gBACb,yBAAyB;gBACzB,uBAAuB;gBACvB,qBAAqB;YACvB;YACA,SAAS;gBACP,kBAAkB;gBAClB,mBAAmB;YACrB;QACF;QAEA,QAAQ,GAAG,CAAC,mCAAmC,SAAS,MAAM;QAC9D,QAAQ,GAAG,CAAC,sCAAsC,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;QAEhF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,SAAS,IAAI;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}