{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/NEXTSELLING/src/app/api/amazon/search/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport axios from 'axios'\n\nconst RAPIDAPI_KEY = process.env.RAPIDAPI_KEY!\nconst BASE_URL = 'https://real-time-amazon-data.p.rapidapi.com'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const query = searchParams.get('query')\n    const page = parseInt(searchParams.get('page') || '1')\n    const country = searchParams.get('country') || 'US'\n\n    if (!query) {\n      return NextResponse.json(\n        { error: 'Query parameter is required' },\n        { status: 400 }\n      )\n    }\n\n    console.log('🔍 Making request to Amazon API with query:', query)\n\n    // If no API key, return mock data for testing\n    if (!RAPIDAPI_KEY || RAPIDAPI_KEY === 'your_rapidapi_key_here') {\n      console.log('⚠️ No valid API key found, returning mock data')\n      const mockData = {\n        data: [\n          {\n            asin: 'B07ZPKN6YR',\n            product_title: `Mock ${query} Product 1`,\n            product_price: '$29.99',\n            product_original_price: '$39.99',\n            currency: 'USD',\n            product_star_rating: '4.5',\n            product_num_ratings: 1247,\n            product_url: 'https://amazon.com/mock-product-1',\n            product_photo: 'https://via.placeholder.com/300x300?text=Mock+Product+1',\n            product_availability: 'In Stock',\n            is_best_seller: true,\n            is_amazon_choice: false,\n            is_prime: true,\n            climate_pledge_friendly: false,\n            sales_volume: '100+ bought in past month',\n            delivery: 'FREE delivery'\n          },\n          {\n            asin: 'B08XYZABC1',\n            product_title: `Mock ${query} Product 2`,\n            product_price: '$49.99',\n            product_original_price: '$59.99',\n            currency: 'USD',\n            product_star_rating: '4.2',\n            product_num_ratings: 892,\n            product_url: 'https://amazon.com/mock-product-2',\n            product_photo: 'https://via.placeholder.com/300x300?text=Mock+Product+2',\n            product_availability: 'In Stock',\n            is_best_seller: false,\n            is_amazon_choice: true,\n            is_prime: true,\n            climate_pledge_friendly: true,\n            sales_volume: '50+ bought in past month',\n            delivery: 'FREE delivery'\n          }\n        ],\n        total_products: 1000,\n        country: 'US',\n        domain: 'amazon.com'\n      }\n      return NextResponse.json(mockData)\n    }\n\n    const response = await axios.get(`${BASE_URL}/search`, {\n      params: {\n        query,\n        page: page.toString(),\n        country,\n        sort_by: 'RELEVANCE',\n        product_condition: 'ALL',\n        is_prime: 'false',\n        deals_and_discounts: 'NONE'\n      },\n      headers: {\n        'x-rapidapi-key': RAPIDAPI_KEY,\n        'x-rapidapi-host': 'real-time-amazon-data.p.rapidapi.com'\n      }\n    })\n\n    console.log('📦 Amazon API response status:', response.status)\n    console.log('📦 Amazon API response data keys:', Object.keys(response.data || {}))\n    console.log('📦 Amazon API response data:', JSON.stringify(response.data, null, 2))\n\n    return NextResponse.json(response.data)\n  } catch (error) {\n    console.error('Amazon search API error:', error)\n    return NextResponse.json(\n      { error: 'Failed to search products' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { query, page = 1, country = 'US' } = body\n\n    if (!query) {\n      return NextResponse.json(\n        { error: 'Query is required' },\n        { status: 400 }\n      )\n    }\n\n    const results = await AmazonAPIService.searchProducts(query, page, country)\n\n    return NextResponse.json(results)\n  } catch (error) {\n    console.error('Amazon search API error:', error)\n    return NextResponse.json(\n      { error: 'Failed to search products' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,eAAe,QAAQ,GAAG,CAAC,YAAY;AAC7C,MAAM,WAAW;AAEV,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,UAAU,aAAa,GAAG,CAAC,cAAc;QAE/C,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,+CAA+C;QAE3D,8CAA8C;QAC9C,IAAI,CAAC,gBAAgB,iBAAiB,0BAA0B;YAC9D,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW;gBACf,MAAM;oBACJ;wBACE,MAAM;wBACN,eAAe,CAAC,KAAK,EAAE,MAAM,UAAU,CAAC;wBACxC,eAAe;wBACf,wBAAwB;wBACxB,UAAU;wBACV,qBAAqB;wBACrB,qBAAqB;wBACrB,aAAa;wBACb,eAAe;wBACf,sBAAsB;wBACtB,gBAAgB;wBAChB,kBAAkB;wBAClB,UAAU;wBACV,yBAAyB;wBACzB,cAAc;wBACd,UAAU;oBACZ;oBACA;wBACE,MAAM;wBACN,eAAe,CAAC,KAAK,EAAE,MAAM,UAAU,CAAC;wBACxC,eAAe;wBACf,wBAAwB;wBACxB,UAAU;wBACV,qBAAqB;wBACrB,qBAAqB;wBACrB,aAAa;wBACb,eAAe;wBACf,sBAAsB;wBACtB,gBAAgB;wBAChB,kBAAkB;wBAClB,UAAU;wBACV,yBAAyB;wBACzB,cAAc;wBACd,UAAU;oBACZ;iBACD;gBACD,gBAAgB;gBAChB,SAAS;gBACT,QAAQ;YACV;YACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,SAAS,OAAO,CAAC,EAAE;YACrD,QAAQ;gBACN;gBACA,MAAM,KAAK,QAAQ;gBACnB;gBACA,SAAS;gBACT,mBAAmB;gBACnB,UAAU;gBACV,qBAAqB;YACvB;YACA,SAAS;gBACP,kBAAkB;gBAClB,mBAAmB;YACrB;QACF;QAEA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,MAAM;QAC7D,QAAQ,GAAG,CAAC,qCAAqC,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;QAC/E,QAAQ,GAAG,CAAC,gCAAgC,KAAK,SAAS,CAAC,SAAS,IAAI,EAAE,MAAM;QAEhF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,SAAS,IAAI;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,UAAU,IAAI,EAAE,GAAG;QAE5C,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,MAAM,iBAAiB,cAAc,CAAC,OAAO,MAAM;QAEnE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}