import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Search,
  BarChart3,
  TrendingUp,
  Users,
  Star,
  CheckCircle,
  ArrowRight
} from 'lucide-react'

const features = [
  {
    icon: Search,
    title: 'Product Research',
    description: 'Search and analyze Amazon products with real-time data and comprehensive insights.'
  },
  {
    icon: BarChart3,
    title: 'Market Analysis',
    description: 'Get detailed market analysis with competition levels, pricing trends, and opportunities.'
  },
  {
    icon: TrendingUp,
    title: 'Review Insights',
    description: 'AI-powered review analysis to identify customer pain points and product strengths.'
  },
  {
    icon: Users,
    title: 'Competitor Intelligence',
    description: 'Track competitors, analyze their strategies, and find market gaps.'
  }
]

const testimonials = [
  {
    name: '<PERSON>',
    role: 'Amazon Seller',
    content: 'NextSelling helped me identify profitable niches and increase my sales by 300%.',
    rating: 5
  },
  {
    name: '<PERSON>',
    role: 'E-commerce Entrepreneur',
    content: 'The review analysis feature is incredible. It saves me hours of manual research.',
    rating: 5
  },
  {
    name: '<PERSON>',
    role: 'Product Manager',
    content: 'Best tool for Amazon market research. The insights are actionable and accurate.',
    rating: 5
  }
]

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-20 text-center">
        <div className="max-w-4xl mx-auto px-4">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            Dominate Amazon with
            <span className="text-blue-600"> AI-Powered</span> Market Analysis
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Discover profitable niches, analyze competitors, and make data-driven decisions
            with our comprehensive Amazon seller intelligence platform.
          </p>
          <div className="flex gap-4 justify-center">
            <Link href="/dashboard">
              <Button size="lg" className="text-lg px-8">
                Start Free Trial
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </Link>
            <Button variant="outline" size="lg" className="text-lg px-8">
              Watch Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Everything You Need to Succeed on Amazon
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our comprehensive suite of tools helps you research, analyze, and optimize
              your Amazon business for maximum profitability.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <Card key={index} className="text-center">
                  <CardHeader>
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <Icon className="w-6 h-6 text-blue-600" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-600">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Trusted by Successful Amazon Sellers
            </h2>
            <p className="text-lg text-gray-600">
              Join thousands of sellers who are growing their business with NextSelling
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center gap-1 mb-2">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <CardDescription className="text-gray-700 italic">
                    "{testimonial.content}"
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div>
                    <p className="font-semibold text-gray-900">{testimonial.name}</p>
                    <p className="text-sm text-gray-600">{testimonial.role}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Transform Your Amazon Business?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Start your free trial today and discover profitable opportunities in minutes.
          </p>
          <Link href="/dashboard">
            <Button size="lg" variant="secondary" className="text-lg px-8">
              Get Started Now
              <CheckCircle className="ml-2 w-5 h-5" />
            </Button>
          </Link>
        </div>
      </section>
    </div>
  )
}
