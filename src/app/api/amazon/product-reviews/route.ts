import { NextRequest, NextResponse } from 'next/server'
import axios from 'axios'

const RAPIDAPI_KEY = process.env.RAPIDAPI_KEY!
const BASE_URL = 'https://real-time-amazon-data.p.rapidapi.com'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const asin = searchParams.get('asin')
    const country = searchParams.get('country') || 'US'

    if (!asin) {
      return NextResponse.json(
        { error: 'ASIN parameter is required' },
        { status: 400 }
      )
    }

    console.log('🔍 Fetching reviews for ASIN:', asin)
    
    // If no API key, return mock reviews for testing
    if (!RAPIDAPI_KEY || RAPIDAPI_KEY === 'your_rapidapi_key_here') {
      console.log('⚠️ No valid API key found, returning mock reviews')
      const mockReviews = {
        data: {
          reviews: [
            {
              review_id: '1',
              review_title: 'Great product!',
              review_comment: 'This product exceeded my expectations. Great quality and fast delivery.',
              review_star_rating: '5',
              review_date: '2024-01-15',
              reviewer_name: '<PERSON>',
              verified_purchase: true,
              helpful_vote_statement: '5 people found this helpful'
            },
            {
              review_id: '2',
              review_title: 'Good value for money',
              review_comment: 'Decent product for the price. Some minor issues but overall satisfied.',
              review_star_rating: '4',
              review_date: '2024-01-10',
              reviewer_name: 'Sarah M.',
              verified_purchase: true,
              helpful_vote_statement: '3 people found this helpful'
            },
            {
              review_id: '3',
              review_title: 'Could be better',
              review_comment: 'The product works but has some quality issues. Customer service was helpful though.',
              review_star_rating: '3',
              review_date: '2024-01-05',
              reviewer_name: 'Mike R.',
              verified_purchase: true,
              helpful_vote_statement: '2 people found this helpful'
            }
          ],
          total_reviews: 1247,
          average_rating: 4.2
        }
      }
      return NextResponse.json(mockReviews)
    }

    const response = await axios.get(`${BASE_URL}/product-reviews`, {
      params: {
        asin,
        country,
        sort_by: 'TOP_REVIEWS',
        star_rating: 'ALL',
        verified_purchases_only: 'false',
        images_or_videos_only: 'false',
        current_format_only: 'false'
      },
      headers: {
        'x-rapidapi-key': RAPIDAPI_KEY,
        'x-rapidapi-host': 'real-time-amazon-data.p.rapidapi.com'
      }
    })
    
    console.log('📦 Reviews API response status:', response.status)
    console.log('📦 Reviews API response data keys:', Object.keys(response.data || {}))
    
    return NextResponse.json(response.data)
  } catch (error) {
    console.error('Reviews API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch product reviews' },
      { status: 500 }
    )
  }
}
