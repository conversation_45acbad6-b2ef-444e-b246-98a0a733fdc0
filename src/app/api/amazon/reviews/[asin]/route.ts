import { NextRequest, NextResponse } from 'next/server'
import axios from 'axios'

const RAPIDAPI_KEY = process.env.RAPIDAPI_KEY!
const BASE_URL = 'https://real-time-amazon-data.p.rapidapi.com'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ asin: string }> }
) {
  try {
    const { asin } = await params
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const country = searchParams.get('country') || 'US'
    const sortBy = searchParams.get('sortBy') || 'TOP_REVIEWS'

    if (!asin) {
      return NextResponse.json(
        { error: 'ASIN parameter is required' },
        { status: 400 }
      )
    }

    const response = await axios.get(`${BASE_URL}/product-reviews`, {
      params: {
        asin,
        country,
        page: page.toString(),
        sort_by: sortBy,
        star_rating: 'ALL',
        verified_purchases_only: 'false',
        images_or_videos_only: 'false',
        current_format_only: 'false'
      },
      headers: {
        'x-rapidapi-key': RAPIDAPI_KEY,
        'x-rapidapi-host': 'real-time-amazon-data.p.rapidapi.com'
      }
    })

    return NextResponse.json(response.data)
  } catch (error) {
    console.error('Amazon reviews API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch product reviews' },
      { status: 500 }
    )
  }
}
