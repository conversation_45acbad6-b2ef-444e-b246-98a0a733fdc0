import { NextRequest, NextResponse } from 'next/server'
import axios from 'axios'

const RAPIDAPI_KEY = process.env.RAPIDAPI_KEY!
const BASE_URL = 'https://real-time-amazon-data.p.rapidapi.com'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('query')
    const page = parseInt(searchParams.get('page') || '1')
    const country = searchParams.get('country') || 'US'

    if (!query) {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      )
    }

    const response = await axios.get(`${BASE_URL}/search`, {
      params: {
        query,
        page: page.toString(),
        country,
        sort_by: 'RELEVANCE',
        product_condition: 'ALL',
        is_prime: 'false',
        deals_and_discounts: 'NONE'
      },
      headers: {
        'x-rapidapi-key': RAPIDAPI_KEY,
        'x-rapidapi-host': 'real-time-amazon-data.p.rapidapi.com'
      }
    })

    return NextResponse.json(response.data)
  } catch (error) {
    console.error('Amazon search API error:', error)
    return NextResponse.json(
      { error: 'Failed to search products' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { query, page = 1, country = 'US' } = body

    if (!query) {
      return NextResponse.json(
        { error: 'Query is required' },
        { status: 400 }
      )
    }

    const results = await AmazonAPIService.searchProducts(query, page, country)

    return NextResponse.json(results)
  } catch (error) {
    console.error('Amazon search API error:', error)
    return NextResponse.json(
      { error: 'Failed to search products' },
      { status: 500 }
    )
  }
}
