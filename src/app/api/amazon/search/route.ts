import { NextRequest, NextResponse } from 'next/server'
import axios from 'axios'

const RAPIDAPI_KEY = process.env.RAPIDAPI_KEY!
const BASE_URL = 'https://real-time-amazon-data.p.rapidapi.com'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('query')
    const page = parseInt(searchParams.get('page') || '1')
    const country = searchParams.get('country') || 'US'

    if (!query) {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      )
    }

    console.log('🔍 Making request to Amazon API with query:', query)

    // If no API key, return mock data for testing
    if (!RAPIDAPI_KEY || RAPIDAPI_KEY === 'your_rapidapi_key_here') {
      console.log('⚠️ No valid API key found, returning mock data')
      const mockData = {
        data: [
          {
            asin: 'B07ZPKN6YR',
            product_title: `Mock ${query} Product 1`,
            product_price: '$29.99',
            product_original_price: '$39.99',
            currency: 'USD',
            product_star_rating: '4.5',
            product_num_ratings: 1247,
            product_url: 'https://amazon.com/mock-product-1',
            product_photo: 'https://via.placeholder.com/300x300?text=Mock+Product+1',
            product_availability: 'In Stock',
            is_best_seller: true,
            is_amazon_choice: false,
            is_prime: true,
            climate_pledge_friendly: false,
            sales_volume: '100+ bought in past month',
            delivery: 'FREE delivery'
          },
          {
            asin: 'B08XYZABC1',
            product_title: `Mock ${query} Product 2`,
            product_price: '$49.99',
            product_original_price: '$59.99',
            currency: 'USD',
            product_star_rating: '4.2',
            product_num_ratings: 892,
            product_url: 'https://amazon.com/mock-product-2',
            product_photo: 'https://via.placeholder.com/300x300?text=Mock+Product+2',
            product_availability: 'In Stock',
            is_best_seller: false,
            is_amazon_choice: true,
            is_prime: true,
            climate_pledge_friendly: true,
            sales_volume: '50+ bought in past month',
            delivery: 'FREE delivery'
          }
        ],
        total_products: 1000,
        country: 'US',
        domain: 'amazon.com'
      }
      return NextResponse.json(mockData)
    }

    const response = await axios.get(`${BASE_URL}/search`, {
      params: {
        query,
        page: page.toString(),
        country,
        sort_by: 'RELEVANCE',
        product_condition: 'ALL',
        is_prime: 'false',
        deals_and_discounts: 'NONE'
      },
      headers: {
        'x-rapidapi-key': RAPIDAPI_KEY,
        'x-rapidapi-host': 'real-time-amazon-data.p.rapidapi.com'
      }
    })

    console.log('📦 Amazon API response status:', response.status)
    console.log('📦 Amazon API response data keys:', Object.keys(response.data || {}))
    console.log('📦 Amazon API response data:', JSON.stringify(response.data, null, 2))

    return NextResponse.json(response.data)
  } catch (error) {
    console.error('Amazon search API error:', error)
    return NextResponse.json(
      { error: 'Failed to search products' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { query, page = 1, country = 'US' } = body

    if (!query) {
      return NextResponse.json(
        { error: 'Query is required' },
        { status: 400 }
      )
    }

    const results = await AmazonAPIService.searchProducts(query, page, country)

    return NextResponse.json(results)
  } catch (error) {
    console.error('Amazon search API error:', error)
    return NextResponse.json(
      { error: 'Failed to search products' },
      { status: 500 }
    )
  }
}
