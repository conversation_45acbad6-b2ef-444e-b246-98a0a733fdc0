import { NextRequest, NextResponse } from 'next/server'
import axios from 'axios'

const RAPIDAPI_KEY = process.env.RAPIDAPI_KEY!
const BASE_URL = 'https://real-time-amazon-data.p.rapidapi.com'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ asin: string }> }
) {
  try {
    const { asin } = await params
    const { searchParams } = new URL(request.url)
    const country = searchParams.get('country') || 'US'

    if (!asin) {
      return NextResponse.json(
        { error: 'ASIN parameter is required' },
        { status: 400 }
      )
    }

    const response = await axios.get(`${BASE_URL}/product-details`, {
      params: {
        asin,
        country
      },
      headers: {
        'x-rapidapi-key': RAPIDAPI_KEY,
        'x-rapidapi-host': 'real-time-amazon-data.p.rapidapi.com'
      }
    })

    return NextResponse.json(response.data)
  } catch (error) {
    console.error('Amazon product API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch product details' },
      { status: 500 }
    )
  }
}
