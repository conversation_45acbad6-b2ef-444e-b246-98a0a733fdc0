import { NextRequest, NextResponse } from 'next/server'
import { OpenRouterAIService } from '@/lib/services/openrouter-ai'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { products, niche } = body

    console.log('🤖 Market analysis request received for niche:', niche)
    console.log('📊 Products count:', products?.length)

    if (!products || !Array.isArray(products) || products.length === 0) {
      return NextResponse.json(
        { error: 'Products array is required and must not be empty' },
        { status: 400 }
      )
    }

    if (!niche || typeof niche !== 'string') {
      return NextResponse.json(
        { error: 'Niche parameter is required and must be a string' },
        { status: 400 }
      )
    }

    // Generate analysis using OpenRouter AI service

    const analysis = await OpenRouterAIService.generateMarketAnalysis(products, niche)

    return NextResponse.json(analysis)
  } catch (error) {
    console.error('Market analysis API error:', error)
    return NextResponse.json(
      { error: 'Failed to generate market analysis' },
      { status: 500 }
    )
  }
}
