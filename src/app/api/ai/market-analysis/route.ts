import { NextRequest, NextResponse } from 'next/server'
import { OpenRouterAIService } from '@/lib/services/openrouter-ai'

const API_KEY = process.env.OPENROUTER_API_KEY!

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { products, niche } = body

    console.log('🤖 Market analysis request received for niche:', niche)
    console.log('📊 Products count:', products?.length)

    if (!products || !Array.isArray(products) || products.length === 0) {
      return NextResponse.json(
        { error: 'Products array is required and must not be empty' },
        { status: 400 }
      )
    }

    if (!niche || typeof niche !== 'string') {
      return NextResponse.json(
        { error: 'Niche parameter is required and must be a string' },
        { status: 400 }
      )
    }

    // If no API key, return mock analysis
    if (!API_KEY || API_KEY === 'your_openrouter_api_key_here') {
      console.log('⚠️ No valid OpenRouter API key found, returning mock analysis')
      const mockAnalysis = {
        market_size: `Large market with ${products.length * 100}+ products`,
        competition_level: products.length > 5 ? 'high' : products.length > 2 ? 'medium' : 'low',
        opportunity_score: Math.floor(Math.random() * 40) + 60, // 60-100
        key_insights: [
          `The ${niche} market shows strong demand with consistent sales`,
          `Price points range from $${Math.min(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))} to $${Math.max(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))}`,
          `Customer satisfaction is generally high with average ratings above 4.0`
        ],
        recommended_actions: [
          `Focus on products in the $${Math.floor(Math.random() * 20) + 20}-$${Math.floor(Math.random() * 30) + 40} price range`,
          `Emphasize quality and customer service to compete effectively`,
          `Consider seasonal trends and inventory management for ${niche} products`
        ],
        price_analysis: {
          average_price: Math.floor(products.reduce((sum, p) => sum + parseFloat(p.product_price?.replace('$', '') || '0'), 0) / products.length),
          price_range: {
            min: Math.min(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0'))),
            max: Math.max(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))
          },
          price_trend: ['increasing', 'decreasing', 'stable'][Math.floor(Math.random() * 3)]
        }
      }
      return NextResponse.json(mockAnalysis)
    }

    const analysis = await OpenRouterAIService.generateMarketAnalysis(products, niche)

    return NextResponse.json(analysis)
  } catch (error) {
    console.error('Market analysis API error:', error)
    return NextResponse.json(
      { error: 'Failed to generate market analysis' },
      { status: 500 }
    )
  }
}
