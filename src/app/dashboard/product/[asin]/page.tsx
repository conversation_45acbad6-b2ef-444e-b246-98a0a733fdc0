'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Star,
  ExternalLink,
  TrendingUp,
  MessageSquare,
  BarChart3,
  Package,
  DollarSign,
  Users,
  ThumbsUp,
  ThumbsDown,
  Loader2
} from 'lucide-react'
import { AmazonAPIService, type ProductDetails, type AmazonReview } from '@/lib/services/amazon-api'
import { OpenRouterAIService, type ReviewAnalysisResult } from '@/lib/services/openrouter-ai'
import { formatPrice, formatNumber, getStarRating } from '@/lib/utils'

export default function ProductAnalysis() {
  const params = useParams()
  const asin = params.asin as string

  const [product, setProduct] = useState<ProductDetails | null>(null)
  const [reviews, setReviews] = useState<AmazonReview[]>([])
  const [reviewAnalysis, setReviewAnalysis] = useState<ReviewAnalysisResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [analyzingReviews, setAnalyzingReviews] = useState(false)

  useEffect(() => {
    if (asin) {
      loadProductData()
    }
  }, [asin])

  const loadProductData = async () => {
    try {
      setLoading(true)
      
      // Load product details and reviews in parallel
      const [productData, reviewsData] = await Promise.all([
        AmazonAPIService.getProductDetails(asin),
        AmazonAPIService.getProductReviews(asin, 1)
      ])

      setProduct(productData)
      setReviews(reviewsData.data)
    } catch (error) {
      console.error('Failed to load product data:', error)
    } finally {
      setLoading(false)
    }
  }

  const analyzeReviews = async () => {
    if (!reviews.length) return

    try {
      setAnalyzingReviews(true)
      const reviewTexts = reviews.map(review => review.review_comment).filter(Boolean)
      const analysis = await OpenRouterAIService.analyzeReviews(reviewTexts)
      setReviewAnalysis(analysis)
    } catch (error) {
      console.error('Failed to analyze reviews:', error)
    } finally {
      setAnalyzingReviews(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading product analysis...</p>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="text-center py-12">
        <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Product not found</h3>
        <p className="text-gray-600">The product with ASIN {asin} could not be found.</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Product Analysis</h1>
        <p className="text-gray-600 mt-2">
          Comprehensive analysis for ASIN: {asin}
        </p>
      </div>

      {/* Product Overview */}
      <Card>
        <CardContent className="p-6">
          <div className="flex gap-6">
            <div className="flex-shrink-0">
              <img
                src={product.product_photos[0]}
                alt={product.product_title}
                className="w-48 h-48 object-cover rounded-lg border"
              />
            </div>
            <div className="flex-1 space-y-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  {product.product_title}
                </h2>
                <p className="text-gray-600">ASIN: {asin}</p>
              </div>

              <div className="flex items-center gap-6">
                <div className="flex items-center">
                  <span className="text-yellow-400 mr-2 text-lg">
                    {getStarRating(parseFloat(product.product_star_rating))}
                  </span>
                  <span className="text-lg font-medium">
                    {product.product_star_rating}
                  </span>
                </div>
                <span className="text-gray-600">
                  {formatNumber(product.product_num_ratings)} reviews
                </span>
              </div>

              <div className="flex items-center gap-4">
                <div className="text-3xl font-bold text-gray-900">
                  {product.product_price}
                </div>
                {product.product_original_price && (
                  <div className="text-xl text-gray-500 line-through">
                    {product.product_original_price}
                  </div>
                )}
              </div>

              <div className="flex gap-2">
                <Button asChild>
                  <a 
                    href={product.product_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View on Amazon
                  </a>
                </Button>
                <Button variant="outline">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Market Analysis
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rating</CardTitle>
            <Star className="w-4 h-4 text-yellow-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{product.product_star_rating}</div>
            <p className="text-xs text-gray-600">out of 5 stars</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reviews</CardTitle>
            <MessageSquare className="w-4 h-4 text-blue-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(product.product_num_ratings)}</div>
            <p className="text-xs text-gray-600">customer reviews</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Price</CardTitle>
            <DollarSign className="w-4 h-4 text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{product.product_price}</div>
            <p className="text-xs text-gray-600">current price</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Category</CardTitle>
            <Package className="w-4 h-4 text-purple-400" />
          </CardHeader>
          <CardContent>
            <div className="text-sm font-bold">{product.category_tree[0]?.name || 'N/A'}</div>
            <p className="text-xs text-gray-600">main category</p>
          </CardContent>
        </Card>
      </div>

      {/* Review Analysis */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Review Analysis</CardTitle>
              <CardDescription>
                AI-powered analysis of customer reviews to identify key insights
              </CardDescription>
            </div>
            <Button 
              onClick={analyzeReviews} 
              disabled={analyzingReviews || !reviews.length}
            >
              {analyzingReviews ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <TrendingUp className="w-4 h-4 mr-2" />
              )}
              {analyzingReviews ? 'Analyzing...' : 'Analyze Reviews'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {reviewAnalysis ? (
            <div className="grid md:grid-cols-2 gap-6">
              {/* Pros */}
              <div>
                <h4 className="font-semibold text-green-700 mb-3 flex items-center">
                  <ThumbsUp className="w-4 h-4 mr-2" />
                  Top Positive Points
                </h4>
                <div className="space-y-2">
                  {reviewAnalysis.pros.map((pro, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <span className="text-sm text-green-800">{pro.text}</span>
                      <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded-full">
                        {pro.count} mentions
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Cons */}
              <div>
                <h4 className="font-semibold text-red-700 mb-3 flex items-center">
                  <ThumbsDown className="w-4 h-4 mr-2" />
                  Top Negative Points
                </h4>
                <div className="space-y-2">
                  {reviewAnalysis.cons.map((con, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                      <span className="text-sm text-red-800">{con.text}</span>
                      <span className="text-xs bg-red-200 text-red-800 px-2 py-1 rounded-full">
                        {con.count} mentions
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">
                Click "Analyze Reviews" to get AI-powered insights from customer feedback
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Product Description */}
      <Card>
        <CardHeader>
          <CardTitle>Product Description</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 leading-relaxed">
            {product.product_description || 'No description available.'}
          </p>
        </CardContent>
      </Card>

      {/* Recent Reviews */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Reviews ({reviews.length})</CardTitle>
          <CardDescription>
            Latest customer feedback and ratings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reviews.slice(0, 5).map((review, index) => (
              <div key={index} className="border-b pb-4 last:border-b-0">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-900">{review.review_author}</span>
                    {review.is_verified_purchase && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                        Verified Purchase
                      </span>
                    )}
                  </div>
                  <div className="flex items-center">
                    <span className="text-yellow-400 mr-1">
                      {getStarRating(review.review_star_rating)}
                    </span>
                    <span className="text-sm text-gray-600">{review.review_date}</span>
                  </div>
                </div>
                <h5 className="font-medium text-gray-900 mb-1">{review.review_title}</h5>
                <p className="text-gray-700 text-sm">{review.review_comment}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
