'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Search, 
  BarChart3, 
  TrendingUp, 
  Package,
  ArrowRight,
  Star,
  DollarSign
} from 'lucide-react'
import Link from 'next/link'

const quickStats = [
  {
    title: 'Products Analyzed',
    value: '1,247',
    change: '+12%',
    icon: Package
  },
  {
    title: 'Market Opportunities',
    value: '23',
    change: '+5%',
    icon: TrendingUp
  },
  {
    title: 'Avg. Profit Margin',
    value: '34%',
    change: '+8%',
    icon: DollarSign
  },
  {
    title: 'Success Rate',
    value: '89%',
    change: '+3%',
    icon: Star
  }
]

const recentAnalyses = [
  {
    product: 'Wireless Bluetooth Headphones',
    asin: 'B07ZPKN6YR',
    rating: 4.5,
    reviews: 12847,
    opportunity: 'High',
    date: '2 hours ago'
  },
  {
    product: 'Smart Home Security Camera',
    asin: 'B08XYZABC1',
    rating: 4.2,
    reviews: 8934,
    opportunity: 'Medium',
    date: '5 hours ago'
  },
  {
    product: 'Fitness Tracker Watch',
    asin: 'B09ABCDEF2',
    rating: 4.7,
    reviews: 15623,
    opportunity: 'Low',
    date: '1 day ago'
  }
]

export default function Dashboard() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">
          Welcome back! Here's an overview of your Amazon market analysis.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <Link href="/dashboard/search">
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Search className="w-4 h-4 text-blue-600" />
                </div>
                <CardTitle className="text-lg">Product Search</CardTitle>
              </div>
              <ArrowRight className="w-4 h-4 ml-auto text-gray-400" />
            </CardHeader>
            <CardContent>
              <CardDescription>
                Search and analyze Amazon products by keywords or ASIN to discover market opportunities.
              </CardDescription>
            </CardContent>
          </Link>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <Link href="/dashboard/market-analysis">
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-4 h-4 text-green-600" />
                </div>
                <CardTitle className="text-lg">Market Analysis</CardTitle>
              </div>
              <ArrowRight className="w-4 h-4 ml-auto text-gray-400" />
            </CardHeader>
            <CardContent>
              <CardDescription>
                Get comprehensive market insights and competitive analysis for your niche.
              </CardDescription>
            </CardContent>
          </Link>
        </Card>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <Icon className="w-4 h-4 text-gray-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-green-600 mt-1">
                  {stat.change} from last month
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Recent Analyses */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Product Analyses</CardTitle>
          <CardDescription>
            Your latest product research and market analysis results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentAnalyses.map((analysis, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{analysis.product}</h4>
                  <p className="text-sm text-gray-600">ASIN: {analysis.asin}</p>
                  <div className="flex items-center space-x-4 mt-2">
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600 ml-1">{analysis.rating}</span>
                    </div>
                    <span className="text-sm text-gray-600">{analysis.reviews.toLocaleString()} reviews</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      analysis.opportunity === 'High' ? 'bg-green-100 text-green-800' :
                      analysis.opportunity === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {analysis.opportunity} Opportunity
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">{analysis.date}</p>
                  <Button variant="outline" size="sm" className="mt-2">
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Getting Started */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-blue-900">Getting Started</CardTitle>
          <CardDescription className="text-blue-700">
            New to NextSelling? Here are some quick actions to get you started.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4">
            <Link href="/dashboard/search">
              <Button className="w-full justify-start" variant="outline">
                <Search className="w-4 h-4 mr-2" />
                Search Your First Product
              </Button>
            </Link>
            <Button className="w-full justify-start" variant="outline">
              <BarChart3 className="w-4 h-4 mr-2" />
              Watch Tutorial Video
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
