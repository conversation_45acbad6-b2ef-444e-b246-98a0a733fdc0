'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Search,
  Star,
  ExternalLink,
  TrendingUp,
  Package,
  DollarSign,
  Users
} from 'lucide-react'
import { AmazonAPIService, type AmazonProduct } from '@/lib/services/amazon-api'
import { formatPrice, formatNumber, getStarRating } from '@/lib/utils'
import Link from 'next/link'

export default function ProductSearch() {
  const [query, setQuery] = useState('')
  const [loading, setLoading] = useState(false)
  const [products, setProducts] = useState<AmazonProduct[]>([])
  const [totalProducts, setTotalProducts] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)

  const handleSearch = async (page = 1) => {
    if (!query.trim()) return

    setLoading(true)
    try {
      const response = await AmazonAPIService.searchProducts(query, page)
      console.log('🔍 Search response:', response)

      // Handle different response structures
      let products = []
      let totalProducts = 0

      if (response?.data?.products && Array.isArray(response.data.products)) {
        // Real Amazon API structure: { data: { products: [...], total_products: n } }
        products = response.data.products
        totalProducts = response.data.total_products || 0
      } else if (response?.data && Array.isArray(response.data)) {
        // Mock API structure: { data: [...], total_products: n }
        products = response.data
        totalProducts = response.total_products || 0
      } else {
        console.error('❌ Unexpected search response structure:', response)
        throw new Error('Invalid response structure from Amazon API')
      }

      setProducts(products)
      setTotalProducts(totalProducts)
      setCurrentPage(page)
    } catch (error) {
      console.error('Search failed:', error)
      // Handle error - show toast notification
    } finally {
      setLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(1)
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Product Search</h1>
        <p className="text-gray-600 mt-2">
          Search Amazon products by keywords to discover market opportunities and analyze competition.
        </p>
      </div>

      {/* Search Bar */}
      <Card>
        <CardHeader>
          <CardTitle>Search Products</CardTitle>
          <CardDescription>
            Enter keywords or product names to find Amazon products for analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Input
              placeholder="e.g., wireless headphones, fitness tracker, kitchen gadgets..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
            />
            <Button
              onClick={() => handleSearch(1)}
              disabled={loading || !query.trim()}
              className="px-8"
            >
              {loading ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Search className="w-4 h-4" />
              )}
              {loading ? 'Searching...' : 'Search'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Search Results */}
      {products.length > 0 && (
        <div className="space-y-6">
          {/* Results Header */}
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Search Results ({formatNumber(totalProducts)} products found)
            </h2>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Page {currentPage}</span>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSearch(currentPage - 1)}
                  disabled={currentPage === 1 || loading}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSearch(currentPage + 1)}
                  disabled={loading}
                >
                  Next
                </Button>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="grid gap-6">
            {products.map((product, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex gap-6">
                    {/* Product Image */}
                    <div className="flex-shrink-0">
                      <img
                        src={product.product_photo}
                        alt={product.product_title}
                        className="w-32 h-32 object-cover rounded-lg border"
                      />
                    </div>

                    {/* Product Details */}
                    <div className="flex-1 space-y-3">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                          {product.product_title}
                        </h3>
                        <p className="text-sm text-gray-600">ASIN: {product.asin}</p>
                      </div>

                      {/* Rating and Reviews */}
                      <div className="flex items-center gap-4">
                        <div className="flex items-center">
                          <span className="text-yellow-400 mr-1">
                            {getStarRating(parseFloat(product.product_star_rating))}
                          </span>
                          <span className="text-sm text-gray-600">
                            {product.product_star_rating}
                          </span>
                        </div>
                        <span className="text-sm text-gray-600">
                          {formatNumber(product.product_num_ratings)} reviews
                        </span>
                      </div>

                      {/* Price and Badges */}
                      <div className="flex items-center gap-4">
                        <div className="text-2xl font-bold text-gray-900">
                          {product.product_price}
                        </div>
                        {product.product_original_price && (
                          <div className="text-lg text-gray-500 line-through">
                            {product.product_original_price}
                          </div>
                        )}
                        <div className="flex gap-2">
                          {product.is_best_seller && (
                            <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                              Best Seller
                            </span>
                          )}
                          {product.is_amazon_choice && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              Amazon's Choice
                            </span>
                          )}
                          {product.is_prime && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              Prime
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Quick Stats */}
                      <div className="grid grid-cols-3 gap-4 pt-2">
                        <div className="text-center p-2 bg-gray-50 rounded">
                          <div className="text-sm font-medium text-gray-900">Availability</div>
                          <div className="text-xs text-gray-600">{product.product_availability}</div>
                        </div>
                        <div className="text-center p-2 bg-gray-50 rounded">
                          <div className="text-sm font-medium text-gray-900">Sales Volume</div>
                          <div className="text-xs text-gray-600">{product.sales_volume || 'N/A'}</div>
                        </div>
                        <div className="text-center p-2 bg-gray-50 rounded">
                          <div className="text-sm font-medium text-gray-900">Delivery</div>
                          <div className="text-xs text-gray-600">{product.delivery || 'Standard'}</div>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex-shrink-0 flex flex-col gap-2">
                      <Link href={`/dashboard/product/${product.asin}`}>
                        <Button className="w-full">
                          <TrendingUp className="w-4 h-4 mr-2" />
                          Analyze
                        </Button>
                      </Link>
                      <Button variant="outline" size="sm" asChild>
                        <a
                          href={product.product_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center"
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          View on Amazon
                        </a>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {products.length === 0 && !loading && (
        <Card className="text-center py-12">
          <CardContent>
            <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No products found
            </h3>
            <p className="text-gray-600 mb-6">
              {query ? 'Try searching with different keywords or check your spelling.' : 'Enter keywords above to search for Amazon products.'}
            </p>
            <div className="flex justify-center gap-4">
              <Button variant="outline">
                <Search className="w-4 h-4 mr-2" />
                Search Examples
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
