'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  DollarSign,
  Users,
  Package,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react'
import { OpenRouterAIService, type MarketAnalysisResult } from '@/lib/services/openrouter-ai'
import { AmazonAPIService } from '@/lib/services/amazon-api'

export default function MarketAnalysis() {
  const [niche, setNiche] = useState('')
  const [loading, setLoading] = useState(false)
  const [analysis, setAnalysis] = useState<MarketAnalysisResult | null>(null)

  const handleAnalyze = async () => {
    if (!niche.trim()) return

    setLoading(true)
    try {
      // First, search for products in the niche
      console.log('🔍 Searching for products with niche:', niche)
      const searchResults = await AmazonAPIService.searchProducts(niche, 1)

      console.log('📦 Search results received:', searchResults)
      console.log('📦 Search results type:', typeof searchResults)
      console.log('📦 Search results keys:', Object.keys(searchResults || {}))

      // Handle different response structures
      let products = []
      if (searchResults?.data?.products && Array.isArray(searchResults.data.products)) {
        // Real Amazon API structure: { data: { products: [...] } }
        products = searchResults.data.products
      } else if (searchResults?.data && Array.isArray(searchResults.data)) {
        // Mock API structure: { data: [...] }
        products = searchResults.data
      } else if (Array.isArray(searchResults)) {
        // Direct array
        products = searchResults
      } else if (searchResults?.products && Array.isArray(searchResults.products)) {
        // Alternative structure: { products: [...] }
        products = searchResults.products
      } else {
        console.error('❌ Unexpected response structure:', searchResults)
        throw new Error('Invalid response structure from Amazon API')
      }

      console.log('📊 Products array:', products)
      console.log('📊 Products count:', products.length)

      if (products.length === 0) {
        throw new Error('No products found for this niche')
      }

      // Generate market analysis
      console.log('🤖 Generating market analysis for', products.length, 'products')
      const marketAnalysis = await OpenRouterAIService.generateMarketAnalysis(
        products.slice(0, 10), // Analyze top 10 products
        niche
      )

      console.log('✅ Market analysis completed:', marketAnalysis)
      setAnalysis(marketAnalysis)
    } catch (error) {
      console.error('❌ Market analysis failed:', error)
      // Handle error - show toast notification
    } finally {
      setLoading(false)
    }
  }

  const getCompetitionColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'high': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getOpportunityColor = (score: number) => {
    if (score >= 70) return 'text-green-600'
    if (score >= 40) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="w-4 h-4 text-green-600" />
      case 'decreasing': return <TrendingDown className="w-4 h-4 text-red-600" />
      default: return <BarChart3 className="w-4 h-4 text-gray-600" />
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Market Analysis</h1>
        <p className="text-gray-600 mt-2">
          Analyze market opportunities and competition for any niche on Amazon.
        </p>
      </div>

      {/* Analysis Input */}
      <Card>
        <CardHeader>
          <CardTitle>Niche Market Analysis</CardTitle>
          <CardDescription>
            Enter a product niche or category to get comprehensive market insights
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Input
              placeholder="e.g., wireless earbuds, yoga mats, coffee makers..."
              value={niche}
              onChange={(e) => setNiche(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAnalyze()}
              className="flex-1"
            />
            <Button
              onClick={handleAnalyze}
              disabled={loading || !niche.trim()}
              className="px-8"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <BarChart3 className="w-4 h-4" />
              )}
              {loading ? 'Analyzing...' : 'Analyze Market'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Analysis Results */}
      {analysis && (
        <div className="space-y-6">
          {/* Market Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Market Size</CardTitle>
                <Package className="w-4 h-4 text-blue-400" />
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold">{analysis.market_size}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Competition Level</CardTitle>
                <Users className="w-4 h-4 text-purple-400" />
              </CardHeader>
              <CardContent>
                <div className={`text-lg font-bold capitalize px-3 py-1 rounded-full inline-block ${getCompetitionColor(analysis.competition_level)}`}>
                  {analysis.competition_level}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Opportunity Score</CardTitle>
                <TrendingUp className="w-4 h-4 text-green-400" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getOpportunityColor(analysis.opportunity_score)}`}>
                  {analysis.opportunity_score}/100
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Price</CardTitle>
                <DollarSign className="w-4 h-4 text-green-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${analysis.price_analysis.average_price}</div>
                <div className="flex items-center text-sm text-gray-600 mt-1">
                  {getTrendIcon(analysis.price_analysis.price_trend)}
                  <span className="ml-1 capitalize">{analysis.price_analysis.price_trend}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Price Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Price Analysis</CardTitle>
              <CardDescription>
                Pricing insights and trends for the {niche} market
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">
                    ${analysis.price_analysis.price_range.min}
                  </div>
                  <div className="text-sm text-gray-600">Minimum Price</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-900">
                    ${analysis.price_analysis.average_price}
                  </div>
                  <div className="text-sm text-blue-600">Average Price</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">
                    ${analysis.price_analysis.price_range.max}
                  </div>
                  <div className="text-sm text-gray-600">Maximum Price</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Key Insights */}
          <Card>
            <CardHeader>
              <CardTitle>Key Market Insights</CardTitle>
              <CardDescription>
                Important findings and observations about the {niche} market
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analysis.key_insights.map((insight, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <p className="text-blue-900">{insight}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recommended Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Recommended Actions</CardTitle>
              <CardDescription>
                Strategic recommendations based on the market analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analysis.recommended_actions.map((action, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                    <AlertCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <p className="text-green-900">{action}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Empty State */}
      {!analysis && !loading && (
        <Card className="text-center py-12">
          <CardContent>
            <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No Analysis Yet
            </h3>
            <p className="text-gray-600 mb-6">
              Enter a product niche above to get comprehensive market insights and competitive analysis.
            </p>
            <div className="flex justify-center gap-4">
              <Button variant="outline">
                <Package className="w-4 h-4 mr-2" />
                View Examples
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
