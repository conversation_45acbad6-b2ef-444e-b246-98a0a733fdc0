'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  DollarSign,
  Users,
  Package,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react'
import { OpenRouterAIService, type MarketAnalysisResult } from '@/lib/services/openrouter-ai'
import { AmazonAPIService } from '@/lib/services/amazon-api'

export default function MarketAnalysis() {
  const [niche, setNiche] = useState('')
  const [loading, setLoading] = useState(false)
  const [analysis, setAnalysis] = useState<MarketAnalysisResult | null>(null)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)
  const [productAnalysisLoading, setProductAnalysisLoading] = useState(false)

  const handleAnalyze = async () => {
    if (!niche.trim()) return

    setLoading(true)
    try {
      // First, search for products in the niche - get more products for top 20 analysis
      console.log('🔍 Searching for products with niche:', niche)
      const searchResults = await AmazonAPIService.searchProducts(niche, 1)

      console.log('📦 Search results received:', searchResults)
      console.log('📦 Search results type:', typeof searchResults)
      console.log('📦 Search results keys:', Object.keys(searchResults || {}))

      // Handle different response structures
      let products = []
      if (searchResults?.data?.products && Array.isArray(searchResults.data.products)) {
        // Real Amazon API structure: { data: { products: [...] } }
        products = searchResults.data.products
      } else if (searchResults?.data && Array.isArray(searchResults.data)) {
        // Mock API structure: { data: [...] }
        products = searchResults.data
      } else if (Array.isArray(searchResults)) {
        // Direct array
        products = searchResults
      } else if (searchResults?.products && Array.isArray(searchResults.products)) {
        // Alternative structure: { products: [...] }
        products = searchResults.products
      } else {
        console.error('❌ Unexpected response structure:', searchResults)
        throw new Error('Invalid response structure from Amazon API')
      }

      console.log('📊 Products array:', products)
      console.log('📊 Products count:', products.length)

      if (products.length === 0) {
        throw new Error('No products found for this niche')
      }

      // Generate market analysis with top 20 products
      console.log('🤖 Generating market analysis for', products.length, 'products')
      const marketAnalysis = await OpenRouterAIService.generateMarketAnalysis(
        products.slice(0, 20), // Analyze top 20 products
        niche
      )

      console.log('✅ Market analysis completed:', marketAnalysis)
      setAnalysis(marketAnalysis)
    } catch (error) {
      console.error('❌ Market analysis failed:', error)
      // Handle error - show toast notification
    } finally {
      setLoading(false)
    }
  }

  const handleProductAnalysis = async (product: any) => {
    setSelectedProduct(product)
    setProductAnalysisLoading(true)

    try {
      console.log('🔍 Analyzing product:', product.title)

      // 1. Fetch product reviews
      console.log('📝 Fetching reviews for ASIN:', product.asin)
      const reviewsResponse = await fetch(`/api/amazon/product-reviews?asin=${product.asin}&country=US`)

      if (!reviewsResponse.ok) {
        throw new Error('Failed to fetch product reviews')
      }

      const reviewsData = await reviewsResponse.json()
      console.log('📝 Reviews data received:', reviewsData)

      // Extract review texts for analysis
      let reviews = []
      if (reviewsData?.data?.reviews && Array.isArray(reviewsData.data.reviews)) {
        reviews = reviewsData.data.reviews.map((review: any) => review.review_comment).filter(Boolean)
      } else if (Array.isArray(reviewsData?.reviews)) {
        reviews = reviewsData.reviews.map((review: any) => review.review_comment).filter(Boolean)
      }

      console.log('📝 Extracted', reviews.length, 'review texts for analysis')

      if (reviews.length > 0) {
        // 2. Analyze reviews using AI
        console.log('🤖 Analyzing reviews with AI...')
        const reviewAnalysis = await OpenRouterAIService.analyzeReviews(reviews.slice(0, 20)) // Analyze top 20 reviews
        console.log('✅ Review analysis completed:', reviewAnalysis)

        // 3. Show results (you could display this in a modal or new section)
        alert(`Product Analysis Complete!\n\nPros: ${reviewAnalysis.pros?.map(p => p.text).join(', ') || 'N/A'}\n\nCons: ${reviewAnalysis.cons?.map(c => c.text).join(', ') || 'N/A'}\n\nSentiment Score: ${reviewAnalysis.sentiment_score || 'N/A'}/5`)
      } else {
        alert('No reviews found for analysis')
      }

    } catch (error) {
      console.error('❌ Product analysis failed:', error)
      alert('Failed to analyze product. Please try again.')
    } finally {
      setProductAnalysisLoading(false)
    }
  }

  const getCompetitionColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'high': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getOpportunityColor = (score: number) => {
    if (score >= 70) return 'text-green-600'
    if (score >= 40) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="w-4 h-4 text-green-600" />
      case 'decreasing': return <TrendingDown className="w-4 h-4 text-red-600" />
      default: return <BarChart3 className="w-4 h-4 text-gray-600" />
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Market Analysis</h1>
        <p className="text-gray-600 mt-2">
          Analyze market opportunities and competition for any niche on Amazon.
        </p>
      </div>

      {/* Analysis Input */}
      <Card>
        <CardHeader>
          <CardTitle>Niche Market Analysis</CardTitle>
          <CardDescription>
            Enter a product niche or category to get comprehensive market insights
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Input
              placeholder="e.g., wireless earbuds, yoga mats, coffee makers..."
              value={niche}
              onChange={(e) => setNiche(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAnalyze()}
              className="flex-1"
            />
            <Button
              onClick={handleAnalyze}
              disabled={loading || !niche.trim()}
              className="px-8"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <BarChart3 className="w-4 h-4" />
              )}
              {loading ? 'Analyzing...' : 'Analyze Market'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Analysis Results */}
      {analysis && (
        <div className="space-y-6">
          {/* Market Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Market Size</CardTitle>
                <Package className="w-4 h-4 text-blue-400" />
              </CardHeader>
              <CardContent>
                <div className="text-sm font-bold">{analysis.market_size}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Competition Level</CardTitle>
                <Users className="w-4 h-4 text-purple-400" />
              </CardHeader>
              <CardContent>
                <div className={`text-lg font-bold capitalize px-3 py-1 rounded-full inline-block ${getCompetitionColor(analysis.competition_level)}`}>
                  {analysis.competition_level}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Opportunity Score</CardTitle>
                <TrendingUp className="w-4 h-4 text-green-400" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getOpportunityColor(analysis.opportunity_score)}`}>
                  {analysis.opportunity_score}/100
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Price</CardTitle>
                <DollarSign className="w-4 h-4 text-green-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${analysis.price_analysis.average_price}</div>
                <div className="flex items-center text-sm text-gray-600 mt-1">
                  {getTrendIcon(analysis.price_analysis.price_trend)}
                  <span className="ml-1 capitalize">{analysis.price_analysis.price_trend}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Price Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Price Analysis & Profit Margins</CardTitle>
              <CardDescription>
                Comprehensive pricing insights and profit potential for the {niche} market
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h4 className="font-semibold mb-4">Price Range</h4>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-xl font-bold text-gray-900">
                        ${analysis.price_analysis.price_range.min}
                      </div>
                      <div className="text-sm text-gray-600">Minimum</div>
                    </div>
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-xl font-bold text-blue-900">
                        ${analysis.price_analysis.average_price}
                      </div>
                      <div className="text-sm text-blue-600">Average</div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-xl font-bold text-gray-900">
                        ${analysis.price_analysis.price_range.max}
                      </div>
                      <div className="text-sm text-gray-600">Maximum</div>
                    </div>
                  </div>
                </div>
                {analysis.price_analysis.profit_margins && (
                  <div>
                    <h4 className="font-semibold mb-4">Profit Margins</h4>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-xl font-bold text-green-900">
                          ${analysis.price_analysis.profit_margins.low_end}
                        </div>
                        <div className="text-sm text-green-600">Low End</div>
                      </div>
                      <div className="text-center p-4 bg-green-100 rounded-lg">
                        <div className="text-xl font-bold text-green-900">
                          ${analysis.price_analysis.profit_margins.average}
                        </div>
                        <div className="text-sm text-green-600">Average</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-xl font-bold text-green-900">
                          ${analysis.price_analysis.profit_margins.high_end}
                        </div>
                        <div className="text-sm text-green-600">High End</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Competitor Analysis */}
          {analysis.competitor_analysis && (
            <Card>
              <CardHeader>
                <CardTitle>Competitor Analysis</CardTitle>
                <CardDescription>
                  Key players and competitive landscape in the {niche} market
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="font-semibold mb-4">Top Brands</h4>
                    <div className="space-y-2">
                      {analysis.competitor_analysis.top_brands.map((brand, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
                            {index + 1}
                          </div>
                          <span className="font-medium">{brand}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-4">Market Leaders</h4>
                    <div className="space-y-4">
                      {analysis.competitor_analysis.market_leaders.map((leader, index) => (
                        <div key={index} className="p-4 border rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <h5 className="font-semibold">{leader.brand}</h5>
                            <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              {leader.market_share}% share
                            </span>
                          </div>
                          <div className="text-sm text-gray-600">
                            <strong>Key Advantages:</strong> {leader.key_advantages.join(', ')}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                {analysis.competitor_analysis.competitive_gaps.length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-semibold mb-4">Competitive Gaps & Opportunities</h4>
                    <div className="grid md:grid-cols-2 gap-4">
                      {analysis.competitor_analysis.competitive_gaps.map((gap, index) => (
                        <div key={index} className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                          <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                          <p className="text-yellow-900">{gap}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Key Insights */}
          <Card>
            <CardHeader>
              <CardTitle>Key Market Insights</CardTitle>
              <CardDescription>
                Important findings and observations about the {niche} market
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analysis.key_insights.map((insight, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <p className="text-blue-900">{insight}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recommended Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Recommended Actions</CardTitle>
              <CardDescription>
                Strategic recommendations based on the market analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analysis.recommended_actions.map((action, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                    <AlertCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <p className="text-green-900">{action}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top 20 Products */}
          {analysis.top_products && analysis.top_products.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Top 20 Products in {niche}</CardTitle>
                <CardDescription>
                  Best-performing products with detailed analysis - click any product for review insights
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {analysis.top_products.map((product, index) => (
                    <div
                      key={product.asin}
                      className="border rounded-lg p-4 hover:shadow-lg transition-shadow cursor-pointer"
                      onClick={() => handleProductAnalysis(product)}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                            product.market_position === 'leader' ? 'bg-yellow-100 text-yellow-800' :
                            product.market_position === 'challenger' ? 'bg-blue-100 text-blue-800' :
                            product.market_position === 'follower' ? 'bg-gray-100 text-gray-800' :
                            'bg-purple-100 text-purple-800'
                          }`}>
                            {index + 1}
                          </div>
                          {product.is_best_seller && (
                            <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                              Best Seller
                            </span>
                          )}
                        </div>
                        <div className={`text-xs px-2 py-1 rounded-full ${
                          product.market_position === 'leader' ? 'bg-yellow-100 text-yellow-800' :
                          product.market_position === 'challenger' ? 'bg-blue-100 text-blue-800' :
                          product.market_position === 'follower' ? 'bg-gray-100 text-gray-800' :
                          'bg-purple-100 text-purple-800'
                        }`}>
                          {product.market_position}
                        </div>
                      </div>

                      <h4 className="font-semibold text-sm mb-2 line-clamp-2">
                        {product.title}
                      </h4>

                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Price:</span>
                          <span className="font-semibold">{product.price}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Rating:</span>
                          <span className="flex items-center gap-1">
                            <span className="font-semibold">{product.rating}</span>
                            <span className="text-gray-500">({product.reviews})</span>
                          </span>
                        </div>
                        {product.sales_volume && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">Sales:</span>
                            <span className="text-sm">{product.sales_volume}</span>
                          </div>
                        )}
                      </div>

                      <div className="mt-3 p-2 bg-gray-50 rounded text-xs">
                        <strong>Advantage:</strong> {product.competitive_advantage}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full mt-3"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleProductAnalysis(product)
                        }}
                      >
                        {productAnalysisLoading && selectedProduct?.asin === product.asin ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          'Analyze Reviews'
                        )}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Empty State */}
      {!analysis && !loading && (
        <Card className="text-center py-12">
          <CardContent>
            <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No Analysis Yet
            </h3>
            <p className="text-gray-600 mb-6">
              Enter a product niche above to get comprehensive market insights and competitive analysis.
            </p>
            <div className="flex justify-center gap-4">
              <Button variant="outline">
                <Package className="w-4 h-4 mr-2" />
                View Examples
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
