import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatPrice(price: number) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(price)
}

export function formatNumber(num: number) {
  return new Intl.NumberFormat('en-US').format(num)
}

export function truncateText(text: string, maxLength: number) {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

export function getStarRating(rating: number) {
  const stars = []
  const fullStars = Math.floor(rating)
  const hasHalfStar = rating % 1 !== 0

  for (let i = 0; i < fullStars; i++) {
    stars.push('★')
  }

  if (hasHalfStar) {
    stars.push('☆')
  }

  while (stars.length < 5) {
    stars.push('☆')
  }

  return stars.join('')
}

export function extractASIN(url: string): string | null {
  const asinRegex = /\/([A-Z0-9]{10})(?:[/?]|$)/
  const match = url.match(asinRegex)
  return match ? match[1] : null
}

export function isValidASIN(asin: string): boolean {
  return /^[A-Z0-9]{10}$/.test(asin)
}
