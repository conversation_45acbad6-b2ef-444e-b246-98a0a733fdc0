import axios from 'axios'

const RAPIDAPI_KEY = process.env.RAPIDAPI_KEY!
const BASE_URL = 'https://real-time-amazon-data.p.rapidapi.com'

const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: {
    'x-rapidapi-key': RAPIDAPI_KEY,
    'x-rapidapi-host': 'real-time-amazon-data.p.rapidapi.com'
  }
})

export interface AmazonProduct {
  asin: string
  product_title: string
  product_price: string
  product_original_price?: string
  currency: string
  product_star_rating: string
  product_num_ratings: number
  product_url: string
  product_photo: string
  product_availability: string
  is_best_seller: boolean
  is_amazon_choice: boolean
  is_prime: boolean
  climate_pledge_friendly: boolean
  sales_volume?: string
  delivery?: string
}

export interface AmazonReview {
  review_id: string
  review_title: string
  review_comment: string
  review_star_rating: number
  review_date: string
  review_author: string
  review_author_avatar?: string
  is_verified_purchase: boolean
  helpful_vote_statement?: string
  review_country: string
  review_images?: string[]
  review_videos?: string[]
}

export interface ProductDetails {
  asin: string
  product_title: string
  product_price: string
  product_original_price?: string
  currency: string
  product_star_rating: string
  product_num_ratings: number
  product_url: string
  product_photos: string[]
  product_details: Record<string, any>
  product_description: string
  product_information: Record<string, any>
  product_specifications: Record<string, any>
  customers_say: {
    keywords: Array<{
      keyword: string
      sentiment_score: number
      mentions_count: number
    }>
  }
  category_tree: Array<{
    name: string
    link: string
  }>
}

export class AmazonAPIService {
  static async searchProducts(query: string, page = 1, country = 'US'): Promise<{
    data: AmazonProduct[]
    total_products: number
    country: string
    domain: string
  }> {
    try {
      const response = await fetch(`/api/amazon/search?query=${encodeURIComponent(query)}&page=${page}&country=${country}`)
      if (!response.ok) {
        throw new Error('Failed to search products')
      }
      return await response.json()
    } catch (error) {
      console.error('Error searching products:', error)
      throw new Error('Failed to search products')
    }
  }

  static async getProductDetails(asin: string, country = 'US'): Promise<ProductDetails> {
    try {
      const response = await fetch(`/api/amazon/product/${asin}?country=${country}`)
      if (!response.ok) {
        throw new Error('Failed to fetch product details')
      }
      return await response.json()
    } catch (error) {
      console.error('Error fetching product details:', error)
      throw new Error('Failed to fetch product details')
    }
  }

  static async getProductReviews(
    asin: string,
    page = 1,
    country = 'US',
    sortBy = 'TOP_REVIEWS'
  ): Promise<{
    data: AmazonReview[]
    total_reviews: number
    country: string
    domain: string
  }> {
    try {
      const response = await fetch(`/api/amazon/reviews/${asin}?page=${page}&country=${country}&sortBy=${sortBy}`)
      if (!response.ok) {
        throw new Error('Failed to fetch product reviews')
      }
      return await response.json()
    } catch (error) {
      console.error('Error fetching product reviews:', error)
      throw new Error('Failed to fetch product reviews')
    }
  }

  static async getProductsByCategory(
    categoryId: string,
    page = 1,
    country = 'US'
  ): Promise<{
    data: AmazonProduct[]
    total_products: number
    country: string
    domain: string
  }> {
    try {
      const response = await apiClient.get('/products-by-category', {
        params: {
          category_id: categoryId,
          page: page.toString(),
          country
        }
      })
      return response.data
    } catch (error) {
      console.error('Error fetching products by category:', error)
      throw new Error('Failed to fetch products by category')
    }
  }
}
