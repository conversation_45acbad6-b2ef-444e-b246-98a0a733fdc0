const API_KEY = process.env.OPENROUTER_API_KEY!

export interface ReviewAnalysisResult {
  pros: Array<{ text: string; count: number }>
  cons: Array<{ text: string; count: number }>
  sentiment_score?: number
  summary?: string
}

export interface MarketAnalysisResult {
  market_size: string
  competition_level: 'low' | 'medium' | 'high'
  opportunity_score: number
  key_insights: string[]
  recommended_actions: string[]
  price_analysis: {
    average_price: number
    price_range: { min: number; max: number }
    price_trend: 'increasing' | 'decreasing' | 'stable'
  }
}

export class OpenRouterAIService {
  static async analyzeReviews(
    reviews: string[],
    locale: 'en' | 'fr' = 'en'
  ): Promise<ReviewAnalysisResult> {
    try {
      const response = await fetch('/api/ai/analyze-reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reviews, locale })
      })

      if (!response.ok) {
        throw new Error('Failed to analyze reviews')
      }

      return await response.json()
    } catch (error) {
      console.error('Error analyzing reviews:', error)
      throw new Error('Failed to analyze reviews')
    }
  }

  static async generateMarketAnalysis(
    products: any[],
    niche: string
  ): Promise<MarketAnalysisResult> {
    try {
      const response = await fetch('/api/ai/market-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ products, niche })
      })

      if (!response.ok) {
        throw new Error('Failed to generate market analysis')
      }

      return await response.json()
    } catch (error) {
      console.error('Error generating market analysis:', error)
      throw new Error('Failed to generate market analysis')
    }
  }

  static async generateProductInsights(
    productDetails: any,
    reviews: string[]
  ): Promise<{
    strengths: string[]
    weaknesses: string[]
    opportunities: string[]
    threats: string[]
    recommendations: string[]
  }> {
    try {
      const requestBody = {
        model: "deepseek/deepseek-r1:free",
        messages: [{
          role: "user",
          content: `Analyze this product and its reviews to provide strategic insights:

                   Product: ${productDetails.product_title}
                   Price: ${productDetails.product_price}
                   Rating: ${productDetails.product_star_rating}
                   Reviews Count: ${productDetails.product_num_ratings}

                   Sample Reviews: ${reviews.slice(0, 50).join('\n')}

                   Provide a SWOT analysis and recommendations in JSON format:
                   {
                     "strengths": ["strength1", "strength2", "strength3"],
                     "weaknesses": ["weakness1", "weakness2", "weakness3"],
                     "opportunities": ["opportunity1", "opportunity2", "opportunity3"],
                     "threats": ["threat1", "threat2", "threat3"],
                     "recommendations": ["rec1", "rec2", "rec3"]
                   }

                   IMPORTANT: Respond ONLY with valid JSON, no markdown or backticks.`
        }]
      }

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${API_KEY}`,
          "HTTP-Referer": "https://github.com/amazon-product-insights",
          "X-Title": "Amazon Product Insights",
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`)
      }

      const data = await response.json()
      const content = data.choices[0]?.message?.content

      if (!content) {
        throw new Error('No content received from AI service')
      }

      // Clean the response and parse JSON
      const cleanedContent = content.replace(/```json|```/g, '').trim()
      return JSON.parse(cleanedContent)
    } catch (error) {
      console.error('Error generating product insights:', error)
      throw new Error('Failed to generate product insights')
    }
  }
}
