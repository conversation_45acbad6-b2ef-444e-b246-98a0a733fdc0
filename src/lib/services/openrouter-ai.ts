const API_KEY = process.env.OPENROUTER_API_KEY!

export interface ReviewAnalysisResult {
  pros: Array<{ text: string; count: number }>
  cons: Array<{ text: string; count: number }>
  sentiment_score?: number
  summary?: string
}

export interface MarketAnalysisResult {
  market_size: string
  competition_level: 'low' | 'medium' | 'high'
  opportunity_score: number
  key_insights: string[]
  recommended_actions: string[]
  price_analysis: {
    average_price: number
    price_range: { min: number; max: number }
    price_trend: 'increasing' | 'decreasing' | 'stable'
  }
}

export class OpenRouterAIService {
  static async analyzeReviews(
    reviews: string[],
    locale: 'en' | 'fr' = 'en'
  ): Promise<ReviewAnalysisResult> {
    try {
      const response = await fetch('/api/ai/analyze-reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reviews, locale })
      })

      if (!response.ok) {
        throw new Error('Failed to analyze reviews')
      }

      return await response.json()
    } catch (error) {
      console.error('Error analyzing reviews:', error)
      throw new Error('Failed to analyze reviews')
    }
  }

  static async generateMarketAnalysis(
    products: any[],
    niche: string
  ): Promise<MarketAnalysisResult> {
    try {
      console.log('🤖 Generating market analysis with OpenRouter AI for niche:', niche)
      console.log('📊 Products to analyze:', products.length)

      // If no API key, return mock analysis
      if (!API_KEY || API_KEY === 'your_openrouter_api_key_here') {
        console.log('⚠️ No valid OpenRouter API key found, returning mock analysis')
        const competitionLevel = products.length > 5 ? 'high' : products.length > 2 ? 'medium' : 'low'
        const priceTrend = ['increasing', 'decreasing', 'stable'][Math.floor(Math.random() * 3)] as 'increasing' | 'decreasing' | 'stable'

        const mockAnalysis: MarketAnalysisResult = {
          market_size: `Large market with ${products.length * 100}+ products`,
          competition_level: competitionLevel as 'low' | 'medium' | 'high',
          opportunity_score: Math.floor(Math.random() * 40) + 60, // 60-100
          key_insights: [
            `The ${niche} market shows strong demand with consistent sales`,
            `Price points range from $${Math.min(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))} to $${Math.max(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))}`,
            `Customer satisfaction is generally high with average ratings above 4.0`
          ],
          recommended_actions: [
            `Focus on products in the $${Math.floor(Math.random() * 20) + 20}-$${Math.floor(Math.random() * 30) + 40} price range`,
            `Emphasize quality and customer service to compete effectively`,
            `Consider seasonal trends and inventory management for ${niche} products`
          ],
          price_analysis: {
            average_price: Math.floor(products.reduce((sum, p) => sum + parseFloat(p.product_price?.replace('$', '') || '0'), 0) / products.length),
            price_range: {
              min: Math.min(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0'))),
              max: Math.max(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))
            },
            price_trend: priceTrend
          }
        }
        return mockAnalysis
      }

      // Prepare product data for AI analysis
      const productSummary = products.slice(0, 10).map(p => ({
        title: p.product_title,
        price: p.product_price,
        rating: p.product_star_rating,
        reviews: p.product_num_ratings,
        is_best_seller: p.is_best_seller,
        sales_volume: p.sales_volume
      }))

      const requestBody = {
        model: "deepseek/deepseek-r1:free",
        messages: [{
          role: "user",
          content: `Analyze the ${niche} market based on these Amazon products and provide comprehensive market insights:

Products Data:
${JSON.stringify(productSummary, null, 2)}

Provide a detailed market analysis in JSON format:
{
  "market_size": "description of market size",
  "competition_level": "low|medium|high",
  "opportunity_score": number_between_0_and_100,
  "key_insights": ["insight1", "insight2", "insight3"],
  "recommended_actions": ["action1", "action2", "action3"],
  "price_analysis": {
    "average_price": number,
    "price_range": {"min": number, "max": number},
    "price_trend": "increasing|decreasing|stable"
  }
}

IMPORTANT: Respond ONLY with valid JSON, no markdown or backticks.`
        }]
      }

      console.log('🚀 Making request to OpenRouter API...')
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${API_KEY}`,
          "HTTP-Referer": "https://github.com/amazon-market-analysis",
          "X-Title": "Amazon Market Analysis",
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new Error(`OpenRouter API request failed: ${response.statusText}`)
      }

      const data = await response.json()
      const content = data.choices[0]?.message?.content

      if (!content) {
        throw new Error('No content received from OpenRouter AI service')
      }

      console.log('✅ Received response from OpenRouter AI')

      // Clean the response and parse JSON
      const cleanedContent = content.replace(/```json|```/g, '').trim()
      return JSON.parse(cleanedContent)
    } catch (error) {
      console.error('❌ Error generating market analysis:', error)
      throw new Error('Failed to generate market analysis')
    }
  }

  static async generateProductInsights(
    productDetails: any,
    reviews: string[]
  ): Promise<{
    strengths: string[]
    weaknesses: string[]
    opportunities: string[]
    threats: string[]
    recommendations: string[]
  }> {
    try {
      const requestBody = {
        model: "deepseek/deepseek-r1:free",
        messages: [{
          role: "user",
          content: `Analyze this product and its reviews to provide strategic insights:

                   Product: ${productDetails.product_title}
                   Price: ${productDetails.product_price}
                   Rating: ${productDetails.product_star_rating}
                   Reviews Count: ${productDetails.product_num_ratings}

                   Sample Reviews: ${reviews.slice(0, 50).join('\n')}

                   Provide a SWOT analysis and recommendations in JSON format:
                   {
                     "strengths": ["strength1", "strength2", "strength3"],
                     "weaknesses": ["weakness1", "weakness2", "weakness3"],
                     "opportunities": ["opportunity1", "opportunity2", "opportunity3"],
                     "threats": ["threat1", "threat2", "threat3"],
                     "recommendations": ["rec1", "rec2", "rec3"]
                   }

                   IMPORTANT: Respond ONLY with valid JSON, no markdown or backticks.`
        }]
      }

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${API_KEY}`,
          "HTTP-Referer": "https://github.com/amazon-product-insights",
          "X-Title": "Amazon Product Insights",
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`)
      }

      const data = await response.json()
      const content = data.choices[0]?.message?.content

      if (!content) {
        throw new Error('No content received from AI service')
      }

      // Clean the response and parse JSON
      const cleanedContent = content.replace(/```json|```/g, '').trim()
      return JSON.parse(cleanedContent)
    } catch (error) {
      console.error('Error generating product insights:', error)
      throw new Error('Failed to generate product insights')
    }
  }
}
