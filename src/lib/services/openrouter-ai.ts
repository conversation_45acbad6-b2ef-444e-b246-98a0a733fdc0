const API_KEY = process.env.OPENROUTER_API_KEY!

export interface ReviewAnalysisResult {
  pros: Array<{ text: string; count: number }>
  cons: Array<{ text: string; count: number }>
  sentiment_score?: number
  summary?: string
}

export interface MarketAnalysisResult {
  market_size: string
  competition_level: 'low' | 'medium' | 'high'
  opportunity_score: number
  key_insights: string[]
  recommended_actions: string[]
  price_analysis: {
    average_price: number
    price_range: { min: number; max: number }
    price_trend: 'increasing' | 'decreasing' | 'stable'
    profit_margins: {
      low_end: number
      high_end: number
      average: number
    }
  }
  competitor_analysis: {
    top_brands: string[]
    market_leaders: {
      brand: string
      market_share: number
      key_advantages: string[]
    }[]
    competitive_gaps: string[]
  }
  customer_analysis: {
    target_demographics: string[]
    buying_patterns: string[]
    pain_points: string[]
    satisfaction_level: number
  }
  market_trends: {
    seasonal_patterns: string
    growth_trajectory: 'growing' | 'stable' | 'declining'
    emerging_features: string[]
    market_maturity: 'emerging' | 'growing' | 'mature' | 'declining'
  }
  risk_assessment: {
    market_risks: string[]
    competitive_threats: string[]
    opportunity_risks: string[]
    risk_level: 'low' | 'medium' | 'high'
  }
  entry_strategy: {
    recommended_price_point: {
      min: number
      max: number
      optimal: number
    }
    differentiation_opportunities: string[]
    marketing_channels: string[]
    timeline_to_market: string
  }
  top_products: Array<{
    asin: string
    title: string
    price: string
    rating: string
    reviews: number
    sales_volume: string
    is_best_seller: boolean
    competitive_advantage: string
    market_position: 'leader' | 'challenger' | 'follower' | 'niche'
  }>
}

export class OpenRouterAIService {
  static async analyzeReviews(
    reviews: string[],
    locale: 'en' | 'fr' = 'en'
  ): Promise<ReviewAnalysisResult> {
    try {
      const response = await fetch('/api/ai/analyze-reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reviews, locale })
      })

      if (!response.ok) {
        throw new Error('Failed to analyze reviews')
      }

      return await response.json()
    } catch (error) {
      console.error('Error analyzing reviews:', error)
      throw new Error('Failed to analyze reviews')
    }
  }

  static async generateMarketAnalysis(
    products: any[],
    niche: string
  ): Promise<MarketAnalysisResult> {
    try {
      console.log('🤖 Generating market analysis with OpenRouter AI for niche:', niche)
      console.log('📊 Products to analyze:', products.length)

      // If no API key, return mock analysis
      if (!API_KEY || API_KEY === 'your_openrouter_api_key_here') {
        console.log('⚠️ No valid OpenRouter API key found, returning mock analysis')
        const competitionLevel = products.length > 5 ? 'high' : products.length > 2 ? 'medium' : 'low'
        const priceTrend = ['increasing', 'decreasing', 'stable'][Math.floor(Math.random() * 3)] as 'increasing' | 'decreasing' | 'stable'

        const avgPrice = Math.floor(products.reduce((sum, p) => sum + parseFloat(p.product_price?.replace('$', '') || '0'), 0) / products.length)
        const minPrice = Math.min(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))
        const maxPrice = Math.max(...products.map(p => parseFloat(p.product_price?.replace('$', '') || '0')))

        const mockAnalysis: MarketAnalysisResult = {
          market_size: `Large market with ${products.length * 100}+ products and $${Math.floor(avgPrice * products.length * 1000)}M+ annual revenue`,
          competition_level: competitionLevel as 'low' | 'medium' | 'high',
          opportunity_score: Math.floor(Math.random() * 40) + 60, // 60-100
          key_insights: [
            `The ${niche} market shows strong demand with consistent sales across ${products.length} analyzed products`,
            `Price points range from $${minPrice} to $${maxPrice} with healthy profit margins`,
            `Customer satisfaction is generally high with average ratings above 4.0 stars`,
            `Market shows ${priceTrend} price trends indicating ${priceTrend === 'increasing' ? 'growing demand' : priceTrend === 'decreasing' ? 'price competition' : 'market stability'}`,
            `Best-selling products dominate with ${products.filter(p => p.is_best_seller).length} out of ${products.length} products having best-seller status`
          ],
          recommended_actions: [
            `Target the $${Math.floor(avgPrice * 0.8)}-$${Math.floor(avgPrice * 1.2)} price range for optimal market entry`,
            `Focus on quality and customer service to compete with established brands`,
            `Leverage emerging features and customer pain points for differentiation`,
            `Consider seasonal trends and inventory management for ${niche} products`,
            `Build strong brand presence through targeted marketing channels`
          ],
          price_analysis: {
            average_price: avgPrice,
            price_range: { min: minPrice, max: maxPrice },
            price_trend: priceTrend,
            profit_margins: {
              low_end: Math.floor(minPrice * 0.3),
              high_end: Math.floor(maxPrice * 0.6),
              average: Math.floor(avgPrice * 0.45)
            }
          },
          competitor_analysis: {
            top_brands: ['Apple', 'Samsung', 'Sony', 'JBL', 'Anker'].slice(0, 3),
            market_leaders: [
              {
                brand: 'Market Leader',
                market_share: 35,
                key_advantages: ['Brand recognition', 'Distribution network', 'Product quality']
              },
              {
                brand: 'Strong Challenger',
                market_share: 25,
                key_advantages: ['Competitive pricing', 'Innovation', 'Customer service']
              }
            ],
            competitive_gaps: [
              'Limited options in mid-range pricing',
              'Lack of eco-friendly alternatives',
              'Poor customer support in some segments'
            ]
          },
          customer_analysis: {
            target_demographics: ['Tech enthusiasts', 'Professionals', 'Budget-conscious consumers'],
            buying_patterns: ['Research-driven purchases', 'Price comparison shopping', 'Review-influenced decisions'],
            pain_points: ['Complex setup', 'Durability concerns', 'Limited warranty coverage'],
            satisfaction_level: 4.2
          },
          market_trends: {
            seasonal_patterns: 'Peak sales during Q4 holidays and back-to-school season',
            growth_trajectory: 'growing' as const,
            emerging_features: ['Wireless connectivity', 'AI integration', 'Sustainability focus'],
            market_maturity: 'growing' as const
          },
          risk_assessment: {
            market_risks: ['Economic downturn impact', 'Supply chain disruptions', 'Technology obsolescence'],
            competitive_threats: ['New market entrants', 'Price wars', 'Patent disputes'],
            opportunity_risks: ['Market saturation', 'Changing consumer preferences', 'Regulatory changes'],
            risk_level: competitionLevel as 'low' | 'medium' | 'high'
          },
          entry_strategy: {
            recommended_price_point: {
              min: Math.floor(avgPrice * 0.8),
              max: Math.floor(avgPrice * 1.2),
              optimal: avgPrice
            },
            differentiation_opportunities: [
              'Superior customer support',
              'Innovative features',
              'Competitive pricing',
              'Better user experience'
            ],
            marketing_channels: ['Amazon PPC', 'Social media advertising', 'Influencer partnerships', 'Content marketing'],
            timeline_to_market: '3-6 months for product development and launch'
          },
          top_products: products.slice(0, 20).map((product, index) => ({
            asin: product.asin,
            title: product.product_title,
            price: product.product_price || 'N/A',
            rating: product.product_star_rating,
            reviews: product.product_num_ratings,
            sales_volume: product.sales_volume || 'N/A',
            is_best_seller: product.is_best_seller || false,
            competitive_advantage: index < 5 ? 'Market leader with strong brand' : index < 10 ? 'Strong challenger with good value' : index < 15 ? 'Solid follower with niche appeal' : 'Emerging player with potential',
            market_position: (index < 3 ? 'leader' : index < 8 ? 'challenger' : index < 15 ? 'follower' : 'niche') as 'leader' | 'challenger' | 'follower' | 'niche'
          }))
        }
        return mockAnalysis
      }

      // Prepare product data for AI analysis
      const productSummary = products.slice(0, 10).map(p => ({
        title: p.product_title,
        price: p.product_price,
        rating: p.product_star_rating,
        reviews: p.product_num_ratings,
        is_best_seller: p.is_best_seller,
        sales_volume: p.sales_volume
      }))

      const requestBody = {
        model: "deepseek/deepseek-r1:free",
        messages: [{
          role: "user",
          content: `Analyze the ${niche} market based on these Amazon products and provide comprehensive market insights:

Products Data:
${JSON.stringify(productSummary, null, 2)}

Provide a detailed market analysis in JSON format with ALL the following fields:
{
  "market_size": "detailed description of market size with revenue estimates",
  "competition_level": "low|medium|high",
  "opportunity_score": number_between_0_and_100,
  "key_insights": ["insight1", "insight2", "insight3", "insight4", "insight5"],
  "recommended_actions": ["action1", "action2", "action3", "action4", "action5"],
  "price_analysis": {
    "average_price": number,
    "price_range": {"min": number, "max": number},
    "price_trend": "increasing|decreasing|stable",
    "profit_margins": {
      "low_end": number,
      "high_end": number,
      "average": number
    }
  },
  "competitor_analysis": {
    "top_brands": ["brand1", "brand2", "brand3"],
    "market_leaders": [
      {
        "brand": "brand_name",
        "market_share": number,
        "key_advantages": ["advantage1", "advantage2", "advantage3"]
      }
    ],
    "competitive_gaps": ["gap1", "gap2", "gap3"]
  },
  "customer_analysis": {
    "target_demographics": ["demo1", "demo2", "demo3"],
    "buying_patterns": ["pattern1", "pattern2", "pattern3"],
    "pain_points": ["pain1", "pain2", "pain3"],
    "satisfaction_level": number_between_1_and_5
  },
  "market_trends": {
    "seasonal_patterns": "description of seasonal trends",
    "growth_trajectory": "growing|stable|declining",
    "emerging_features": ["feature1", "feature2", "feature3"],
    "market_maturity": "emerging|growing|mature|declining"
  },
  "risk_assessment": {
    "market_risks": ["risk1", "risk2", "risk3"],
    "competitive_threats": ["threat1", "threat2", "threat3"],
    "opportunity_risks": ["risk1", "risk2", "risk3"],
    "risk_level": "low|medium|high"
  },
  "entry_strategy": {
    "recommended_price_point": {
      "min": number,
      "max": number,
      "optimal": number
    },
    "differentiation_opportunities": ["opp1", "opp2", "opp3"],
    "marketing_channels": ["channel1", "channel2", "channel3"],
    "timeline_to_market": "timeline description"
  },
  "top_products": [
    {
      "asin": "product_asin",
      "title": "product_title",
      "price": "product_price",
      "rating": "product_rating",
      "reviews": number_of_reviews,
      "sales_volume": "sales_volume",
      "is_best_seller": boolean,
      "competitive_advantage": "advantage description",
      "market_position": "leader|challenger|follower|niche"
    }
  ]
}

IMPORTANT: Respond ONLY with valid JSON, no markdown or backticks. Include ALL fields in the response.`
        }]
      }

      console.log('🚀 Making request to OpenRouter API...')
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${API_KEY}`,
          "HTTP-Referer": "https://github.com/amazon-market-analysis",
          "X-Title": "Amazon Market Analysis",
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new Error(`OpenRouter API request failed: ${response.statusText}`)
      }

      const data = await response.json()
      const content = data.choices[0]?.message?.content

      if (!content) {
        throw new Error('No content received from OpenRouter AI service')
      }

      console.log('✅ Received response from OpenRouter AI')

      // Clean the response and parse JSON
      const cleanedContent = content.replace(/```json|```/g, '').trim()
      return JSON.parse(cleanedContent)
    } catch (error) {
      console.error('❌ Error generating market analysis:', error)
      throw new Error('Failed to generate market analysis')
    }
  }

  static async generateProductInsights(
    productDetails: any,
    reviews: string[]
  ): Promise<{
    strengths: string[]
    weaknesses: string[]
    opportunities: string[]
    threats: string[]
    recommendations: string[]
  }> {
    try {
      const requestBody = {
        model: "deepseek/deepseek-r1:free",
        messages: [{
          role: "user",
          content: `Analyze this product and its reviews to provide strategic insights:

                   Product: ${productDetails.product_title}
                   Price: ${productDetails.product_price}
                   Rating: ${productDetails.product_star_rating}
                   Reviews Count: ${productDetails.product_num_ratings}

                   Sample Reviews: ${reviews.slice(0, 50).join('\n')}

                   Provide a SWOT analysis and recommendations in JSON format:
                   {
                     "strengths": ["strength1", "strength2", "strength3"],
                     "weaknesses": ["weakness1", "weakness2", "weakness3"],
                     "opportunities": ["opportunity1", "opportunity2", "opportunity3"],
                     "threats": ["threat1", "threat2", "threat3"],
                     "recommendations": ["rec1", "rec2", "rec3"]
                   }

                   IMPORTANT: Respond ONLY with valid JSON, no markdown or backticks.`
        }]
      }

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${API_KEY}`,
          "HTTP-Referer": "https://github.com/amazon-product-insights",
          "X-Title": "Amazon Product Insights",
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`)
      }

      const data = await response.json()
      const content = data.choices[0]?.message?.content

      if (!content) {
        throw new Error('No content received from AI service')
      }

      // Clean the response and parse JSON
      const cleanedContent = content.replace(/```json|```/g, '').trim()
      return JSON.parse(cleanedContent)
    } catch (error) {
      console.error('Error generating product insights:', error)
      throw new Error('Failed to generate product insights')
    }
  }
}
