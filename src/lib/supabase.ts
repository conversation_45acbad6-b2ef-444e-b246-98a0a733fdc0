import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export function createSupabaseClient() {
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Database types
export interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  subscription_tier: 'free' | 'pro' | 'enterprise'
  created_at: string
  updated_at: string
}

export interface SearchHistory {
  id: string
  user_id: string
  query: string
  results_count: number
  created_at: string
}

export interface ProductAnalysis {
  id: string
  user_id: string
  asin: string
  product_title: string
  price: number
  rating: number
  review_count: number
  analysis_data: any
  created_at: string
  updated_at: string
}

export interface ReviewAnalysis {
  id: string
  product_analysis_id: string
  pros: Array<{ text: string; count: number }>
  cons: Array<{ text: string; count: number }>
  sentiment_score: number
  total_reviews_analyzed: number
  created_at: string
}
