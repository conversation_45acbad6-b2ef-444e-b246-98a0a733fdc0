{"name": "@react-stately/flags", "version": "3.1.1", "description": "Spectrum UI components in React", "license": "Apache-2.0", "main": "dist/main.js", "module": "dist/module.js", "types": "dist/types.d.ts", "exports": {"types": "./dist/types.d.ts", "import": "./dist/import.mjs", "require": "./dist/main.js"}, "source": "src/index.ts", "files": ["dist", "src"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "dependencies": {"@swc/helpers": "^0.5.0"}, "publishConfig": {"access": "public"}, "gitHead": "9b66d270572f482948afee95622a85cdf68ed408"}