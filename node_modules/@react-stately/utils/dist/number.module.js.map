{"mappings": "AAAA;;;;;;;;;;CAUC,GAED;;CAEC,GACM,SAAS,0CAAM,KAAa,EAAE,MAAc,CAAC,QAAQ,EAAE,MAAc,QAAQ;IAClF,IAAI,WAAW,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,MAAM;IAC9C,OAAO;AACT;AAEO,SAAS,0CAAqB,KAAa,EAAE,IAAY;IAC9D,IAAI,eAAe;IACnB,IAAI,aAAa,KAAK,QAAQ;IAC9B,IAAI,aAAa,WAAW,OAAO,CAAC;IACpC,IAAI,YAAY,cAAc,IAAI,WAAW,MAAM,GAAG,aAAa;IACnE,IAAI,YAAY,GAAG;QACjB,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI;QACvB,eAAe,KAAK,KAAK,CAAC,eAAe,OAAO;IAClD;IACA,OAAO;AACT;AAEO,SAAS,0CAAgB,KAAa,EAAE,GAAuB,EAAE,GAAuB,EAAE,IAAY;IAC3G,MAAM,OAAO;IACb,MAAM,OAAO;IACb,IAAI,YAAa,AAAC,CAAA,QAAS,CAAA,MAAM,OAAO,IAAI,GAAE,CAAC,IAAK;IACpD,IAAI,eAAe,0CAAqB,KAAK,GAAG,CAAC,aAAa,KAAK,OAC/D,QAAQ,KAAK,IAAI,CAAC,aAAc,CAAA,OAAO,KAAK,GAAG,CAAC,UAAS,IACzD,QAAQ,WAAW;IAEvB,IAAI,CAAC,MAAM,MAAM;QACf,IAAI,eAAe,KACjB,eAAe;aACV,IAAI,CAAC,MAAM,QAAQ,eAAe,KACvC,eAAe,MAAM,KAAK,KAAK,CAAC,0CAAqB,AAAC,CAAA,MAAM,GAAE,IAAK,MAAM,SAAS;IAEtF,OAAO,IAAI,CAAC,MAAM,QAAQ,eAAe,KACvC,eAAe,KAAK,KAAK,CAAC,0CAAqB,MAAM,MAAM,SAAS;IAGtE,gEAAgE;IAChE,eAAe,0CAAqB,cAAc;IAElD,OAAO;AACT;AAGO,SAAS,yCAAc,KAAa,EAAE,MAAc,EAAE,OAAe,EAAE;IAC5E,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM;IAE3B,OAAO,KAAK,KAAK,CAAC,QAAQ,OAAO;AACnC", "sources": ["packages/@react-stately/utils/src/number.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/**\n * Takes a value and forces it to the closest min/max if it's outside. Also forces it to the closest valid step.\n */\nexport function clamp(value: number, min: number = -Infinity, max: number = Infinity): number {\n  let newValue = Math.min(Math.max(value, min), max);\n  return newValue;\n}\n\nexport function roundToStepPrecision(value: number, step: number): number {\n  let roundedValue = value;\n  let stepString = step.toString();\n  let pointIndex = stepString.indexOf('.');\n  let precision = pointIndex >= 0 ? stepString.length - pointIndex : 0;\n  if (precision > 0) {\n    let pow = Math.pow(10, precision);\n    roundedValue = Math.round(roundedValue * pow) / pow;\n  }\n  return roundedValue;\n}\n\nexport function snapValueToStep(value: number, min: number | undefined, max: number | undefined, step: number): number {\n  min = Number(min);\n  max = Number(max);\n  let remainder = ((value - (isNaN(min) ? 0 : min)) % step);\n  let snappedValue = roundToStepPrecision(Math.abs(remainder) * 2 >= step\n    ? value + Math.sign(remainder) * (step - Math.abs(remainder))\n    : value - remainder, step);\n\n  if (!isNaN(min)) {\n    if (snappedValue < min) {\n      snappedValue = min;\n    } else if (!isNaN(max) && snappedValue > max) {\n      snappedValue = min + Math.floor(roundToStepPrecision((max - min) / step, step)) * step;\n    }\n  } else if (!isNaN(max) && snappedValue > max) {\n    snappedValue = Math.floor(roundToStepPrecision(max / step, step)) * step;\n  }\n\n  // correct floating point behavior by rounding to step precision\n  snappedValue = roundToStepPrecision(snappedValue, step);\n\n  return snappedValue;\n}\n\n/* Takes a value and rounds off to the number of digits. */\nexport function toFixedNumber(value: number, digits: number, base: number = 10): number {\n  const pow = Math.pow(base, digits);\n\n  return Math.round(value * pow) / pow;\n}\n"], "names": [], "version": 3, "file": "number.module.js.map"}