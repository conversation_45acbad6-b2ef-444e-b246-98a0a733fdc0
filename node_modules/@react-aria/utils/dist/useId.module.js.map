{"mappings": ";;;;;AAAA;;;;;;;;;;CAUC;;;;AAOD,mFAAmF;AACnF,IAAI,kCAAY,QACd,OAAO,WAAW,eAClB,OAAO,QAAQ,IACf,OAAO,QAAQ,CAAC,aAAa;AAGxB,IAAI,4CAA2D,IAAI;AAC1E,8EAA8E;AAC9E,6EAA6E;AAC7E,6EAA6E;AAC7E,IAAI;AACJ,IAAI,OAAO,yBAAyB,aAClC,iCAAW,IAAI,qBAA6B,CAAC;IAC3C,0CAAc,MAAM,CAAC;AACvB;AAOK,SAAS,0CAAM,SAAkB;IACtC,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,eAAO,EAAE;IACjC,IAAI,SAAS,CAAA,GAAA,aAAK,EAAE;IAEpB,IAAI,MAAM,CAAA,GAAA,mBAAW,EAAE;IACvB,IAAI,aAAa,CAAA,GAAA,aAAK,EAAE;IAExB,IAAI,gCACF,+BAAS,QAAQ,CAAC,YAAY;IAGhC,IAAI,iCAAW;QACb,MAAM,aAAa,0CAAc,GAAG,CAAC;QACrC,IAAI,cAAc,CAAC,WAAW,QAAQ,CAAC,SACrC,WAAW,IAAI,CAAC;aAEhB,0CAAc,GAAG,CAAC,KAAK;YAAC;SAAO;IAEnC;IAEA,CAAA,GAAA,yCAAc,EAAE;QACd,IAAI,IAAI;QACR,OAAO;YACL,sDAAsD;YACtD,oEAAoE;YACpE,IAAI,gCACF,+BAAS,UAAU,CAAC;YAEtB,0CAAc,MAAM,CAAC;QACvB;IACF,GAAG;QAAC;KAAI;IAER,2EAA2E;IAC3E,2BAA2B;IAC3B,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,QAAQ,OAAO,OAAO;QAC1B,IAAI,OAAS,SAAS;QAEtB,OAAO;YACL,IAAI,OAAS,OAAO,OAAO,GAAG;QAChC;IACF;IAEA,OAAO;AACT;AAMO,SAAS,0CAAS,GAAW,EAAE,GAAW;IAC/C,IAAI,QAAQ,KACV,OAAO;IAGT,IAAI,UAAU,0CAAc,GAAG,CAAC;IAChC,IAAI,SAAS;QACX,QAAQ,OAAO,CAAC,CAAA,MAAQ,IAAI,OAAO,GAAG;QACtC,OAAO;IACT;IAEA,IAAI,UAAU,0CAAc,GAAG,CAAC;IAChC,IAAI,SAAS;QACX,QAAQ,OAAO,CAAC,CAAC,MAAS,IAAI,OAAO,GAAG;QACxC,OAAO;IACT;IAEA,OAAO;AACT;AAOO,SAAS,0CAAU,WAA+B,EAAE;IACzD,IAAI,KAAK;IACT,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,yCAAa,EAAE;IACjD,IAAI,WAAW,CAAA,GAAA,kBAAU,EAAE;QACzB,cAAc;YACZ,MAAM;YAEN,MAAM,SAAS,cAAc,CAAC,MAAM,KAAK;QAC3C;IACF,GAAG;QAAC;QAAI;KAAc;IAEtB,CAAA,GAAA,yCAAc,EAAE,UAAU;QAAC;QAAI;WAAa;KAAS;IAErD,OAAO;AACT", "sources": ["packages/@react-aria/utils/src/useId.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\nimport {useSSRSafeId} from '@react-aria/ssr';\nimport {useValueEffect} from './';\n\n// copied from SSRProvider.tsx to reduce exports, if needed again, consider sharing\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nexport let idsUpdaterMap: Map<string, { current: string | null }[]> = new Map();\n// This allows us to clean up the idsUpdaterMap when the id is no longer used.\n// Map is a strong reference, so unused ids wouldn't be cleaned up otherwise.\n// This can happen in suspended components where mount/unmount is not called.\nlet registry;\nif (typeof FinalizationRegistry !== 'undefined') {\n  registry = new FinalizationRegistry<string>((heldValue) => {\n    idsUpdaterMap.delete(heldValue);\n  });\n}\n\n/**\n * If a default is not provided, generate an id.\n * @param defaultId - Default component id.\n */\nexport function useId(defaultId?: string): string {\n  let [value, setValue] = useState(defaultId);\n  let nextId = useRef(null);\n\n  let res = useSSRSafeId(value);\n  let cleanupRef = useRef(null);\n\n  if (registry) {\n    registry.register(cleanupRef, res);\n  }\n\n  if (canUseDOM) {\n    const cacheIdRef = idsUpdaterMap.get(res);\n    if (cacheIdRef && !cacheIdRef.includes(nextId)) {\n      cacheIdRef.push(nextId);\n    } else {\n      idsUpdaterMap.set(res, [nextId]);\n    }\n  }\n\n  useLayoutEffect(() => {\n    let r = res;\n    return () => {\n      // In Suspense, the cleanup function may be not called\n      // when it is though, also remove it from the finalization registry.\n      if (registry) {\n        registry.unregister(cleanupRef);\n      }\n      idsUpdaterMap.delete(r);\n    };\n  }, [res]);\n\n  // This cannot cause an infinite loop because the ref is always cleaned up.\n  // eslint-disable-next-line\n  useEffect(() => {\n    let newId = nextId.current;\n    if (newId) { setValue(newId); }\n\n    return () => {\n      if (newId) { nextId.current = null; }\n    };\n  });\n\n  return res;\n}\n\n/**\n * Merges two ids.\n * Different ids will trigger a side-effect and re-render components hooked up with `useId`.\n */\nexport function mergeIds(idA: string, idB: string): string {\n  if (idA === idB) {\n    return idA;\n  }\n\n  let setIdsA = idsUpdaterMap.get(idA);\n  if (setIdsA) {\n    setIdsA.forEach(ref => (ref.current = idB));\n    return idB;\n  }\n\n  let setIdsB = idsUpdaterMap.get(idB);\n  if (setIdsB) {\n    setIdsB.forEach((ref) => (ref.current = idA));\n    return idA;\n  }\n\n  return idB;\n}\n\n/**\n * Used to generate an id, and after render, check if that id is rendered so we know\n * if we can use it in places such as labelledby.\n * @param depArray - When to recalculate if the id is in the DOM.\n */\nexport function useSlotId(depArray: ReadonlyArray<any> = []): string {\n  let id = useId();\n  let [resolvedId, setResolvedId] = useValueEffect(id);\n  let updateId = useCallback(() => {\n    setResolvedId(function *() {\n      yield id;\n\n      yield document.getElementById(id) ? id : undefined;\n    });\n  }, [id, setResolvedId]);\n\n  useLayoutEffect(updateId, [id, updateId, ...depArray]);\n\n  return resolvedId;\n}\n"], "names": [], "version": 3, "file": "useId.module.js.map"}