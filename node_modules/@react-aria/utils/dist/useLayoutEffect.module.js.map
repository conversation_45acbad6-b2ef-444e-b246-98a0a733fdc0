{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAOM,MAAM,4CAAkB,OAAO,aAAa,cAC/C,CAAA,GAAA,YAAI,EAAE,eAAe,GACrB,KAAO", "sources": ["packages/@react-aria/utils/src/useLayoutEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport React from 'react';\n\n// During SSR, React emits a warning when calling useLayoutEffect.\n// Since neither useLayoutEffect nor useEffect run on the server,\n// we can suppress this by replace it with a noop on the server.\nexport const useLayoutEffect = typeof document !== 'undefined'\n  ? React.useLayoutEffect\n  : () => {};\n"], "names": [], "version": 3, "file": "useLayoutEffect.module.js.map"}