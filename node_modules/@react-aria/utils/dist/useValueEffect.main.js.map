{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAWM,SAAS,0CAAkB,YAA2B;IAC3D,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qBAAO,EAAE;IACjC,IAAI,SAAgD,CAAA,GAAA,mBAAK,EAAuB;IAEhF,0EAA0E;IAC1E,yCAAyC;IACzC,IAAI,UAAU,CAAA,GAAA,wCAAa,EAAE;QAC3B,IAAI,CAAC,OAAO,OAAO,EACjB;QAEF,uCAAuC;QACvC,IAAI,WAAW,OAAO,OAAO,CAAC,IAAI;QAElC,8CAA8C;QAC9C,IAAI,SAAS,IAAI,EAAE;YACjB,OAAO,OAAO,GAAG;YACjB;QACF;QAEA,iDAAiD;QACjD,8CAA8C;QAC9C,8DAA8D;QAC9D,IAAI,UAAU,SAAS,KAAK,EAC1B;aAEA,SAAS,SAAS,KAAK;IAE3B;IAEA,CAAA,GAAA,yCAAc,EAAE;QACd,uEAAuE;QACvE,IAAI,OAAO,OAAO,EAChB;IAEJ;IAEA,IAAI,QAAQ,CAAA,GAAA,wCAAa,EAAE,CAAA;QACzB,OAAO,OAAO,GAAG,GAAG;QACpB;IACF;IAEA,OAAO;QAAC;QAAO;KAAM;AACvB", "sources": ["packages/@react-aria/utils/src/useValueEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Dispatch, MutableRefObject, useRef, useState} from 'react';\nimport {useEffectEvent, useLayoutEffect} from './';\n\ntype SetValueAction<S> = (prev: S) => Generator<any, void, unknown>;\n\n// This hook works like `useState`, but when setting the value, you pass a generator function\n// that can yield multiple values. Each yielded value updates the state and waits for the next\n// layout effect, then continues the generator. This allows sequential updates to state to be\n// written linearly.\nexport function useValueEffect<S>(defaultValue: S | (() => S)): [S, Dispatch<SetValueAction<S>>] {\n  let [value, setValue] = useState(defaultValue);\n  let effect: MutableRefObject<Generator<S> | null> = useRef<Generator<S> | null>(null);\n\n  // Store the function in a ref so we can always access the current version\n  // which has the proper `value` in scope.\n  let nextRef = useEffectEvent(() => {\n    if (!effect.current) {\n      return;\n    }\n    // Run the generator to the next yield.\n    let newValue = effect.current.next();\n\n    // If the generator is done, reset the effect.\n    if (newValue.done) {\n      effect.current = null;\n      return;\n    }\n\n    // If the value is the same as the current value,\n    // then continue to the next yield. Otherwise,\n    // set the value in state and wait for the next layout effect.\n    if (value === newValue.value) {\n      nextRef();\n    } else {\n      setValue(newValue.value);\n    }\n  });\n\n  useLayoutEffect(() => {\n    // If there is an effect currently running, continue to the next yield.\n    if (effect.current) {\n      nextRef();\n    }\n  });\n\n  let queue = useEffectEvent(fn => {\n    effect.current = fn(value);\n    nextRef();\n  });\n\n  return [value, queue];\n}\n"], "names": [], "version": 3, "file": "useValueEffect.main.js.map"}