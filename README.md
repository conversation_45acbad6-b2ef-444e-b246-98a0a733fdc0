# NextSelling - Amazon Market Analysis SaaS

A comprehensive SaaS platform for Amazon sellers to conduct market analysis, product research, and competitive intelligence using AI-powered insights.

## 🚀 Features

### Core Functionality
- **Product Search & Analysis** - Search Amazon products by keywords or ASIN
- **AI-Powered Review Analysis** - Extract insights from customer reviews using OpenRouter AI
- **Market Intelligence** - Comprehensive market analysis with competition levels and opportunities
- **Real-time Data** - Live Amazon product data via RapidAPI
- **Beautiful Dashboard** - Modern, responsive UI built with Next.js and Tailwind CSS

### Key Capabilities
- 🔍 **Product Research** - Find profitable products and niches
- 📊 **Market Analysis** - Analyze competition, pricing trends, and market size
- 🤖 **AI Review Insights** - Identify pros/cons from customer feedback
- 📈 **Opportunity Scoring** - Get actionable market opportunity scores
- 💰 **Price Analysis** - Track pricing trends and competitive positioning

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **UI Components**: Radix UI, Lucide React Icons
- **APIs**:
  - Amazon Real-Time Data API (RapidAPI)
  - OpenRouter AI (DeepSeek R1)
- **Styling**: Tailwind CSS with custom design system
- **Charts**: Chart.js, Recharts

## 📋 Prerequisites

- Node.js 18+
- npm or yarn
- RapidAPI account for Amazon data
- OpenRouter API account for AI analysis

## ⚙️ Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd nextselling
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.example .env.local
```

Edit `.env.local` with your API keys:
```env
# Amazon API Configuration
RAPIDAPI_KEY=your_rapidapi_key_here

# OpenRouter AI Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

4. **Run the development server**
```bash
npm run dev
```

5. **Open the application**
Navigate to [http://localhost:3000](http://localhost:3000)

## 🔑 API Keys Setup

### RapidAPI (Amazon Data)
1. Sign up at [RapidAPI](https://rapidapi.com)
2. Subscribe to "Real-Time Amazon Data" API
3. Copy your API key to `RAPIDAPI_KEY` in `.env.local`

### OpenRouter AI
1. Sign up at [OpenRouter](https://openrouter.ai)
2. Get your API key
3. Copy it to `OPENROUTER_API_KEY` in `.env.local`

## 📱 Usage

### Product Search
1. Navigate to **Dashboard > Product Search**
2. Enter keywords (e.g., "wireless headphones")
3. Browse search results with ratings, prices, and badges
4. Click "Analyze" on any product for detailed insights

### Product Analysis
1. View comprehensive product details
2. Click "Analyze Reviews" for AI-powered insights
3. See pros/cons extracted from customer reviews
4. Get sentiment analysis and key findings

### Market Analysis
1. Go to **Dashboard > Market Analysis**
2. Enter a niche or category
3. Get market size, competition level, and opportunity score
4. View pricing analysis and strategic recommendations

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   │   ├── amazon/        # Amazon API endpoints
│   │   └── ai/            # AI analysis endpoints
│   ├── dashboard/         # Dashboard pages
│   │   ├── search/        # Product search
│   │   ├── product/       # Product analysis
│   │   └── market-analysis/ # Market insights
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            # Reusable components
│   ├── ui/               # UI components
│   └── navigation.tsx    # Navigation component
└── lib/                  # Utilities and services
    ├── services/         # API services
    │   ├── amazon-api.ts # Amazon API client
    │   └── openrouter-ai.ts # AI service client
    ├── supabase.ts       # Database types
    └── utils.ts          # Utility functions
```

## 🎨 Design System

The application uses a custom design system built on Tailwind CSS with:
- **Color Palette**: Blue primary, semantic colors for status
- **Typography**: Inter font family
- **Components**: Consistent card layouts, buttons, and form elements
- **Responsive Design**: Mobile-first approach
- **Dark Mode Ready**: CSS variables for theme switching

## 🚀 Deployment

### Vercel (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms
The app can be deployed to any platform supporting Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🔮 Future Enhancements

- **User Authentication** - Supabase integration for user accounts
- **Subscription Management** - Stripe integration for paid plans
- **Advanced Analytics** - Historical data tracking and trends
- **Competitor Tracking** - Monitor competitor products and pricing
- **Export Features** - PDF reports and data export
- **Notifications** - Price alerts and market updates
- **Mobile App** - React Native mobile application

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support, email <EMAIL> or join our Discord community.
